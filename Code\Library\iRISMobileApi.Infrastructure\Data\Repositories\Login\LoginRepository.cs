﻿using Dapper;
using iRISEncrypt;
using iRISMobileApi.Dtos.Login;
using iRISMobileApi.Infrastructure.Data.Context;
using iRISMobileApi.Infrastructure.Data.Utils;
using Microsoft.Extensions.Logging;
using System.Data;

namespace iRISMobileApi.Infrastructure.Data.Repositories.Login
{
    public class LoginRepository : ILoginRepository
    {
        private readonly ILogger<LoginRepository> _logger;
        private readonly IrisMobileDBContext _dBContext;

        public LoginRepository(ILogger<LoginRepository> logger, IrisMobileDBContext dBContext)
        {
            _logger = logger;
            _dBContext = dBContext;
            SqlMapper.SetTypeMap(typeof(User), new DataMapper());
        }

        public async Task<User> AuthenticateUserFromDB(LoginRequest loginRequest, string appType)
        {
            User? userData;
            try
            {
                _logger.LogInformation("AuthenticateUserFromDB of LoginRepository is invoked");
                _logger.LogInformation("Username: {0} is getting authenticated from db", loginRequest.userInfo.username);
                string ePassword = Security.Encrypt(loginRequest.userInfo.password);
                var parameters = new DynamicParameters();
                parameters.Add("@UserName", loginRequest.userInfo.username);
                parameters.Add("@Password", ePassword);
                parameters.Add("@ReadTimeStamp", loginRequest.timeInfo?.utcTimeStamp != null ? loginRequest.timeInfo?.utcTimeStamp : DateTime.UtcNow);
                parameters.Add("@ReadTimeStampLocal", loginRequest.timeInfo?.localTimeStamp != null? loginRequest.timeInfo?.localTimeStamp: DateTime.Now);
                parameters.Add("@AppDeviceMACAddress", loginRequest.device?.id);
                parameters.Add("@AppDeviceName", loginRequest.device?.name);
                parameters.Add("@AppDeviceType", loginRequest.device?.type);
                parameters.Add("@AppVersion", loginRequest.requestingApp?.version);
                parameters.Add("@Latitude", loginRequest.device?.locationInfo?.lat);
                parameters.Add("@Longitude", loginRequest.device?.locationInfo?.lon);
                parameters.Add("@TimeZone", loginRequest.timeInfo?.timeZone != null ? loginRequest.timeInfo?.timeZone : "Eastern Standard Time");
                var procName = "";
                if (!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPLoginUserInfo";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPLoginUserInfo";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Specimen"))
                {
                    procName = "uspSPCLoginUserInfo";
                }
                if (!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<User>(procName,
                    parameters,
                    commandType: CommandType.StoredProcedure);
                    if (dbResult.Count() == 0)
                    {
                        _logger.LogInformation("No user info is returned for Username: {0}", loginRequest.userInfo.username);
                        return null;
                    }
                    userData = dbResult.First();
                    _logger.LogInformation("UserInfo for Username: {0} is fetched from db", loginRequest.userInfo.username);
                    return userData;
                } 
                else
                {
                    _logger.LogInformation("No proc mapping for apptype ::: AuthenticateUser", loginRequest.userInfo.username);
                    return null;
                }
                
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling AuthenticateUserFromDB of LoginRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling AuthenticateUserFromDB of LoginRepository: {ex}");
                throw;
            }
        }

        public async Task<List<AppScreenDetails>> FetchAppScreensByUser(string userName)
        {
            List<AppScreenDetails> appScreensList = new List<AppScreenDetails>();
            try
            {
                _logger.LogInformation("FetchAppScreensByUser of LoginRepostiry is invoked");
                _logger.LogInformation("Username: {0} is fetching AppScreens from db", userName);
                var parameters = new DynamicParameters();
                parameters.Add("@UserName", userName);
               
                var dbResult = await _dBContext.CreateConnection().QueryAsync<AppScreenDetails>("uspFetchAppScreensByUser",
                    parameters,
                    commandType: CommandType.StoredProcedure);
                if(dbResult != null)
                {
                    appScreensList = dbResult.ToList();
                }
                
                _logger.LogInformation("AppScreens for Username: {0} are fetched from db", userName);
                return appScreensList;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while fetching AppScreens: {ex.Message}");
                _logger.LogDebug($"Error occured while fetching AppScreens: {ex}");
                throw;
            }
        }

        public async Task<string> FetchDeployedAppType()
        {
            FetchDeployedAppTypeResult? deployedAppTypeData;
            try
            {
                _logger.LogInformation("FetchDeployedAppType of LoginRepository is invoked");
                var dbResult = await _dBContext.CreateConnection().QueryAsync<FetchDeployedAppTypeResult>("uspFetchDeployedAppType",
                    commandType: CommandType.StoredProcedure);
                if (dbResult.Count() == 0)
                {
                    _logger.LogInformation("No AppType is returned in FetchDeployedAppType");
                    return null;
                }
                deployedAppTypeData = dbResult.First();
                if(deployedAppTypeData != null)
                {
                    var appType = deployedAppTypeData.AppType;
                    return appType;
                } 
                else
                {
                    return null;
                }
            } 
            catch(Exception ex)
            {
                _logger.LogError($"Error occured while fetching FetchDeployedAppType: {ex.Message}");
                _logger.LogDebug($"Error occured while fetching FetchDeployedAppType: {ex}");
                throw;
            }
        }
    }
}
