﻿using System.Runtime.Serialization;

namespace iRISMobileApi.Dtos.Products
{
    [Serializable]
    [DataContract]
    public class ProductDetailsDTO
    {
        [DataMember]
        public string ProductID { get; set; }
        [DataMember]
        public string CatalogNo { get; set; }
        [DataMember]
        public string Description { get; set; }
        [DataMember]
        public string Vendor { get; set; }
        [DataMember]
        public string Location { get; set; }
        [DataMember]
        public string Department { get; set; }
        [DataMember]
        public string QtyInCabinet { get; set; }
        [DataMember]
        public string QtyOnShelf { get; set; }
        [DataMember]
        public string TotalQty { get; set; }
    }
}
