﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <UserSecretsId>76c166dc-54ca-4e4a-9817-749782f35857</UserSecretsId>
	<ImplicitUsings>enable</ImplicitUsings>
	<LangVersion>preview</LangVersion>
	<GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Services\**" />
    <Content Remove="Services\**" />
    <EmbeddedResource Remove="Services\**" />
    <None Remove="Services\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.20" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.23" />
    <PackageReference Include="Serilog.AspNetCore" Version="7.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="7.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.3" />
    <PackageReference Include="System.DirectoryServices.AccountManagement" Version="7.0.0" />
  </ItemGroup>
  <ItemGroup>
	<None Update="Microsoft.Data.SqlClient.dll">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Library\iRISMobileApi.Core\iRISMobileApi.Core.csproj" />
    <ProjectReference Include="..\..\Library\iRISMobileApi.Infrastructure\iRISMobileApi.Infrastructure.csproj" />
    <ProjectReference Include="..\iRISMobileApi.Dtos\iRISMobileApi.Dtos.csproj" />
  </ItemGroup>
</Project>
