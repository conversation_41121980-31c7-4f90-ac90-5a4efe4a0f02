﻿using System.Net;
using FluentAssertions;
using iRISMobileApi.Controllers;
using iRISMobileApi.Core.Products;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.Model.Configs;
using iRISMobileApi.Dtos.Products;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Moq;
using Xunit;

namespace iRISMobileApi.UnitTest.Controllers
{
    public class ProductsControllerTest
    {
        private readonly Mock<ILogger<ProductsController>> _logger;
        private readonly Mock<IProductsService> _productsService;
        private readonly IMemoryCache _cache;
        private readonly ProductsController _productsController;
        public ProductsControllerTest()
        {
            _logger = new Mock<ILogger<ProductsController>>();
            _productsService = new Mock<IProductsService>();
            var mockCache = new Mock<IMemoryCache>();

            var mockCacheEntry = new Mock<ICacheEntry>();
            mockCache.Setup(m => m.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);

            object outValue = null;
            mockCache.Setup(m => m.TryGetValue(It.IsAny<object>(), out outValue)).Returns(false);

            _cache = mockCache.Object;

            var cacheSettings = new CacheOptions
            {
                ExpirationScanFrequencyInMinutes = 10,
                ExpirationInMinutes = 10
            };
            var cacheOptions = new Mock<IOptions<CacheOptions>>();
            cacheOptions.Setup(opt => opt.Value).Returns(cacheSettings);

            _productsController = new ProductsController(_logger.Object, _productsService.Object, _cache, cacheOptions.Object);
        }
        [Fact]
        public void Fetch_Products_ok_response()
        {
            //arrange
            ProductsRequest request = new ProductsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******", appType = "Supply", forceRefresh = false },
                productsInfo = new ProductsRequestModel { searchString = "" }
            };
            ProductsResponse mockResponse = new ProductsResponse {
                productsList = new List<dynamic> {
                    new { productId = 2341,
                        catNo = "082700202356",
                        description = "COONS INTERVENTIONAL WIRE GUIDE .035\" 145CM",
                        manufacturer = "COOK MEDICAL INC"
                    },
                    new { productId = 9746,
                        catNo = "23402342",
                        description = "COONS WIRE GUIDE .019\" 129CM",
                        manufacturer = "COOK MEDICAL INC"
                    }
                }
            };
            _productsService
               .Setup(svc => svc.GetProducts(request.requestingApp.appType, request.productsInfo.searchString))
               .ReturnsAsync(mockResponse);
            //act
            var response = _productsController.FetchProducts(request).Result;
            ApiResponse<ProductsResponse> productsResponse = response.Value;

            //assert
            productsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());
            productsResponse.data.productsList.Count.Equals(2);
            productsResponse.data.productsList[0].Equals(mockResponse.productsList[0]);
        }
        [Fact]
        public void Fetch_Products_invalid_request_response()
        {
            //arrange
            //manually adding error that would cause `ModelState.IsValid` to be false
            _productsController.ModelState.AddModelError("productsInfo", "Missing");
            ProductsRequest request = new ProductsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" }
            };
            Error mockError = new Error
            {
                message = "Invalid api request for fetchProducts",
                details = "mandatory fields are missing from the request."
            };

            //act
            var response = _productsController.FetchProducts(request).Result;
            ApiResponse<ProductsResponse> productsResponse = response.Value;

            //assert
            productsResponse.status.Equals(ApiResponseStatus.FAIL.ToString());
            productsResponse.code.Should().Be(HttpStatusCode.BadRequest);
            productsResponse.error.message.Should().Be(mockError.message);
        }
        [Fact]
        public void Fetch_Products_empty_response()
        {
            //arrange
            ProductsRequest request = new ProductsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******", appType = "Supply", forceRefresh = true },
                productsInfo = new ProductsRequestModel { searchString = "123" }
            };
            ProductsResponse mockResponse = new ProductsResponse();
            _productsService
               .Setup(svc => svc.GetProducts(request.requestingApp.appType, request.productsInfo.searchString))
               .ReturnsAsync(mockResponse);

            //act
            var response = _productsController.FetchProducts(request).Result;
            ApiResponse<ProductsResponse> productsResponse = response.Value;

            //assert
            productsResponse.status.Equals(ApiResponseStatus.FAIL.ToString());
            productsResponse.error.message.Equals("no products found for given search characters");
        }
        [Fact]
        public void Fetch_Products_internal_server_error_response()
        {
            //arrange
            ProductsRequest request = new ProductsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                productsInfo = new ProductsRequestModel { searchString = "" }
            };

            var expectedException = new Exception("Internal server error occured");
            _productsService
               .Setup(svc => svc.GetProducts(request.requestingApp.appType, request.productsInfo.searchString))
               .Throws(expectedException);

            //act
            var response = _productsController.FetchProducts(request).Result;
            ApiResponse<ProductsResponse> productsResponse = response.Value;

            //assert
            productsResponse.status.Should().Be(ApiResponseStatus.ERROR);
            productsResponse.code.Should().Be(HttpStatusCode.InternalServerError);
            productsResponse.error.message.Should().Be("Internal server error occured");
        }
        [Fact]
        public void Fetch_ProductDetails_ok_response()
        {
            //arrange
            ProductDetailsRequest request = new ProductDetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                productInfo = new ProductDetailsRequestModel { productId = 1432 }
            };
            ProductDetailsResponse mockResponse = new ProductDetailsResponse
            {
                 productDetailsList = new List<ProductDetails> {
                    new ProductDetails{
                        Location = "MGHIRG2VEN-A",
                        Label_Qty = "Qty",
                        Label_Location = "Location :",
                        TotalQty = 1,
                        productLocationDetails = new List<ProductLocationDetails>{
                        new ProductLocationDetails {
                            Location = "MGHIRG2VEN-A",
                            SerialNo = null,
                            RFID = "E004010833D64391",
                            ItemStatus = "In Cabinet",
                            ScanDate = Convert.ToDateTime("2023-06-12T09:28:08"),
                            LastActivityDate = Convert.ToDateTime("2023-06-12T09:28:08.67"),
                            ExpiredDate = Convert.ToDateTime("2027-10-30T00:00:00"),
                            ScanUser = "LANDRIGAN, CHRISTOPHER",
                            Label_SerialNo = "Serial # :",
                            Label_RFID = "RFID :",
                            Label_Expiration = "Expiration :",
                            Label_ScanDate = "Scan Date :",
                            Label_ScanUser = "Scan By :",
                            Label_Status = "Status :",
                            Label_LastActivityDate = "Last Activity :"
                        } }
                    }
                 }
            };
            _productsService
               .Setup(svc => svc.GetProductDetails(request.requestingApp.appType, request.productInfo.productId))
               .ReturnsAsync(mockResponse);

            //act
            var response = _productsController.FetchProductDetails(request).Result;
            ApiResponse<ProductDetailsResponse> productDetailsResponse = response.Value;

            //assert
            productDetailsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());
            productDetailsResponse.data.productDetailsList.Count.Equals(2);
            productDetailsResponse.data.productDetailsList[0].Equals(mockResponse.productDetailsList[0]);
        }
        [Fact]
        public void Fetch_ProductDetails_invalid_request_response()
        {
            //arrange
            //manually adding error that would cause `ModelState.IsValid` to be false
            _productsController.ModelState.AddModelError("productInfo", "Missing");
            ProductDetailsRequest request = new ProductDetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" }
            };
            Error mockError = new Error
            {
                message = "Invalid api request for fetchProductDetails",
                details = "mandatory fields are missing from the request."
            };

            //act
            var response = _productsController.FetchProductDetails(request).Result;
            ApiResponse<ProductDetailsResponse> productDetailsResponse = response.Value;

            //assert
            productDetailsResponse.status.Equals(ApiResponseStatus.FAIL.ToString());
            productDetailsResponse.code.Should().Be(HttpStatusCode.BadRequest);
            productDetailsResponse.error.message.Should().Be(mockError.message);
        }
        [Fact]
        public void Fetch_ProductDetails_empty_response()
        {
            //arrange
            ProductDetailsRequest request = new ProductDetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                productInfo = new ProductDetailsRequestModel { productId = 123 }
            };
            ProductDetailsResponse mockResponse = new ProductDetailsResponse();
            _productsService
               .Setup(svc => svc.GetProductDetails(request.requestingApp.appType, request.productInfo.productId))
               .ReturnsAsync(mockResponse);

            //act
            var response = _productsController.FetchProductDetails(request).Result;
            ApiResponse<ProductDetailsResponse> productDetailsResponse = response.Value;

            //assert
            productDetailsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());
            productDetailsResponse.data.productDetailsList.IsNullOrEmpty();
        }
        [Fact]
        public void Fetch_ProductDetails_internal_server_error_response()
        {
            //arrange
            ProductDetailsRequest request = new ProductDetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                productInfo = new ProductDetailsRequestModel { productId = 1252 }
            };

            var expectedException = new Exception("Internal server error occured");
            _productsService
               .Setup(svc => svc.GetProductDetails(request.requestingApp.appType, request.productInfo.productId))
               .Throws(expectedException);

            //act
            var response = _productsController.FetchProductDetails(request).Result;
            ApiResponse<ProductDetailsResponse> productDetailsResponse = response.Value;

            //assert
            productDetailsResponse.status.Should().Be(ApiResponseStatus.ERROR);
            productDetailsResponse.code.Should().Be(HttpStatusCode.InternalServerError);
            productDetailsResponse.error.message.Should().Be("Internal server error occured");
        }
    }
}