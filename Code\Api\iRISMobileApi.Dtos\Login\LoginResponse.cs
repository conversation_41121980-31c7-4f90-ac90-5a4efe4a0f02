﻿using iRISMobileApi.Dtos.AppTemplate;

namespace iRISMobileApi.Dtos.Login
{
    public class LoginResponse
    {
        public string token { get; set; }
        public UserDetails user { get; set; }

        public List<WorkFlowDetails> workflows { get; set; }

        public List<AppTemplateDataResponse> appTemplates { get; set; }

        public LoginResponse()
        {
            workflows = new List<WorkFlowDetails>();
            appTemplates = new List<AppTemplateDataResponse>();
        }
    }
}
