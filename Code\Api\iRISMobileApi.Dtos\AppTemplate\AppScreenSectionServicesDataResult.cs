﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iRISMobileApi.Dtos.AppTemplate
{
    public class AppScreenSectionServicesDataResult
    {
        public int AppScreenSectionServiceID { get; set; } 
        
        public int AppScreenSectionID { get; set; } 
        
        public string? AppScreenSectionCode { get; set; }
        
	    public string? AppScreenSectionName { get; set; } 
        
        public string? AppScreenSectionType { get; set; } 
        
        public string? AppScreenCode { get; set; } 
        
        public string? AppScreenName { get; set; }

		public int AppServiceID { get; set; } 
        
        public string? AppServiceCode { get; set; } 
        
        public string? AppServiceName { get; set; } 
        
        public string? AppServiceDescription { get; set; } 
        
        public string? AppServiceCategory { get; set; }
    }
}
