﻿using System.Runtime.Serialization;

namespace iRISMobileApi.Dtos.Reconcile
{
    [Serializable]
    [DataContract]
    public class ReconcileTable
    {
        public ReconcileTable() 
        { 
        
        }

        [DataMember]
        public string Source { get; set; }
        [DataMember]
        public string Processed { get; set; }
        [DataMember]
        public List<TagsData> TagsData { get; set; }
    }
    [Serializable]
    [DataContract]
    public class TagsData
    {
        public TagsData() 
        {
        
        }

        [DataMember]
        public string DateTime { get; set; }
        [DataMember]
        public string Tag { get; set; }
    }
}
