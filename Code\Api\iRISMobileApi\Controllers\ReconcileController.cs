﻿using iRISMobileApi.Core.Reconcile;
using iRISMobileApi.Dtos.Reconcile;
using Microsoft.AspNetCore.Mvc;

namespace iRISMobileApi.Controllers
{
    [ApiController]
    [Route("api/iRISupply/Reconcile")]
    public class ReconcileController : ControllerBase
    {
        private readonly IReconcileService _reconcileService;

        public ReconcileController(IReconcileService reconcileService) 
        {   
            _reconcileService = reconcileService;
        }

        [HttpPost]
        [ApiExplorerSettings(IgnoreApi = true)]
        public ReconcileResponse Post([FromBody] string reconcileData)
        {
            return _reconcileService.ProcessReconcileData(reconcileData);
        }
    }
}
