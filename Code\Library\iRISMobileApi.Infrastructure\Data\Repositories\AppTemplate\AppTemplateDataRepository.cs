﻿using Dapper;
using iRISMobileApi.Dtos.AppTemplate;
using iRISMobileApi.Infrastructure.Data.Context;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iRISMobileApi.Infrastructure.Data.Repositories.AppTemplate
{
    public class AppTemplateDataRepository : IAppTemplateDataRepository
    {
        private readonly ILogger<AppTemplateDataRepository> _logger;
        private readonly IrisMobileDBContext _dBContext;
        public AppTemplateDataRepository(ILogger<AppTemplateDataRepository> logger, IrisMobileDBContext dBContext)
        {
            _logger = logger;
            _dBContext = dBContext;
        }

        public async Task<List<AppTemplateDataResult>> GetAppTemplateData(string? appScreenName)
        {
            List<AppTemplateDataResult> appTemplateDataResult;
            try
            {
                _logger.LogInformation("GetAppTemplateData of AppTemplateDataRepository is invoked");
                if (string.IsNullOrEmpty(appScreenName))
                {
                    appScreenName = null;
                }
                var parameters = new DynamicParameters();
                parameters.Add("@AppScreenName", appScreenName);
                var procName = "uspFetchAppScreenTemplateData";

                if (!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<AppTemplateDataResult>(procName,
                    parameters,
                    commandType: CommandType.StoredProcedure);
                    if (dbResult.Count() == 0)
                    {
                        _logger.LogInformation("No App Template data found");
                        return null;
                    }
                    _logger.LogInformation("App Template data found");
                    appTemplateDataResult = dbResult.ToList();
                }
                else
                {
                    _logger.LogInformation("No proc mapping in GetAppTemplateData ");
                    return null;
                }

            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetAppTemplateData of AppTemplateDataRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetAppTemplateData of AppTemplateDataRepository: {ex}");
                throw;
            }
            return appTemplateDataResult;
        }

        public async Task<List<AppScreenSectionServicesDataResult>> GetAppScreenSectionServicesData()
        {
            List<AppScreenSectionServicesDataResult> appScreenSectionServicesDataResult;
            try
            {
                _logger.LogInformation("GetAppScreenSectionServicesData of AppScreenSectionServicesDataRepository is invoked");
                var procName = "uspFetchAppScreenSectionServices";

                if (!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<AppScreenSectionServicesDataResult>(procName,
                    commandType: CommandType.StoredProcedure);
                    if (dbResult.Count() == 0)
                    {
                        _logger.LogInformation("No App Screen Section Services data found");
                        return null;
                    }
                    _logger.LogInformation("App Screen Section Services data found");
                    appScreenSectionServicesDataResult = dbResult.ToList();
                }
                else
                {
                    _logger.LogInformation("No proc mapping in GetAppScreenSectionServicesData");
                    return null;
                }

            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetAppScreenSectionServicesData of AppTemplateDataRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetAppScreenSectionServicesData of AppTemplateDataRepository: {ex}");
                throw;
            }
            return appScreenSectionServicesDataResult;
        }
    }
}
