﻿using iRISMobileApi.Dtos.ExpirationItems;

namespace iRISMobileApi.Infrastructure.Data.Repositories.ExpiredItem
{
    public interface IExpirationItemsRepository
    {
        Task<List<object>> GetExpirationItems(string? appType, string searchString);

        Task<List<object>> GetExpirationItemsTemplate(string? appType, Dictionary<string, string> parameters);

        Task<List<ExpirationItemDetailsDTO>> GetExpirationItemDetails(string? appType, string catalogNo);

        Task<List<ExpirationItemDetailsDTO>> GetExpirationItemDetailsTemplate(string? appType, Dictionary<string, string> detailsParameters);
    }
}
