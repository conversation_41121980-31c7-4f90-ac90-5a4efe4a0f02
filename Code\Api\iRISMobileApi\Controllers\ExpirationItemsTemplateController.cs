﻿using System.Net;
using iRISMobileApi.Core.ExpiredItem;
using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.Model.Configs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

namespace iRISMobileApi.Controllers
{
    [ApiController]
    [Produces("application/json")]
    public class ExpirationItemsTemplateController : ControllerBase
    {
        private readonly ILogger<ExpirationItemsController> _logger;
        private readonly IExpirationItemsService _expirationItemsService;
        private readonly IMemoryCache _cache;
        IOptions<CacheOptions> _cacheOptions;

        public ExpirationItemsTemplateController(ILogger<ExpirationItemsController> logger, IExpirationItemsService expirationItemsService, IMemoryCache cache, IOptions<CacheOptions> cacheOptions)
        {
            _logger = logger;
            _expirationItemsService = expirationItemsService;
            _cache = cache;
            _cacheOptions = cacheOptions;
        }

        [Route("expiration-items-template"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<List<object>>>> FetchExpirationItemsTemplate([FromBody]ListRequest listRequest)
        {
            ApiResponse<List<object>> result;
            try
            {
                _logger.LogInformation("expiration-items route is invoked");
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<List<object>>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for fetchExpirationItems",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("FetchExpirationItems route generated failure response {0}", result.ToJson());
                    return result;
                }

                string cacheKey = "expiration-items-template";

                List<object> response;

                if (!listRequest.requestingApp.forceRefresh && _cache.TryGetValue(cacheKey, out List<object> data))
                {
                    response = data;
                }
                else
                {
                    response = await _expirationItemsService.GetExpirationItemsTemplate(listRequest.requestingApp.appType, listRequest.listInfo.parameters);
                    if (response == null || response?.Count < 1)
                    {
                        result = new ApiResponse<List<object>>
                        {
                            status = ApiResponseStatus.FAIL,
                            code = HttpStatusCode.NoContent,
                            error = new Error
                            {
                                message = "no expiration items found for given search characters"
                            }
                        };
                        _logger.LogInformation("expiration-items route generated failure response {0}", result.ToJson());
                        return result;
                    }
                    var cacheOptions = new MemoryCacheEntryOptions();
                    cacheOptions.SetAbsoluteExpiration(TimeSpan.FromMinutes(_cacheOptions.Value.ExpirationInMinutes));

                    _cache.Set(cacheKey, response, cacheOptions); result = new ApiResponse<List<object>>
                    {
                        status = ApiResponseStatus.SUCCESS,
                        code = HttpStatusCode.OK,
                        data = response.Take(20).ToList()
                    };
                }
                result = new ApiResponse<List<object>>
                {
                    status = ApiResponseStatus.SUCCESS,
                    code = HttpStatusCode.OK,
                    data = response.Take(20).ToList()
                };
                _logger.LogInformation("expiration-items route generated success response {0}", result.ToJson());
            }
            catch (Exception ex)
            {
                result = new ApiResponse<List<object>>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to fetch expiration items list due to internal server error."
                    }
                };
                _logger.LogError("expiration-items route generated error response {0}", result.ToJson());
            }
            return result;
        }

        [Route("expiration-items-details-template"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<object>>> FetchExpirationItemDetailsTemplate([FromBody]DetailsRequest expirationItemsRequest)
        {
            ApiResponse<object> result;
            try
            {
                _logger.LogInformation("expiration-items route is invoked");
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<object>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for fetchExpirationItemDetailsTemplate",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("FetchExpirationItemDetailsTemplate route generated failure response {0}", result.ToJson());
                    return result;
                }
                string cacheKey = "product-details-template";

                object response;
                if (!expirationItemsRequest.requestingApp.forceRefresh && _cache.TryGetValue(cacheKey, out object data))
                {
                    response = data;
                }
                else
                {
                    response = await _expirationItemsService.GetExpirationItemDetailsTemplate(expirationItemsRequest.requestingApp.appType, expirationItemsRequest.detailsInfo.detailsParameters);
                    if (response == null)
                    {
                        result = new ApiResponse<object>
                        {
                            status = ApiResponseStatus.FAIL,
                            code = HttpStatusCode.NoContent,
                            error = new Error
                            {
                                message = "no expiration items found for given search parameters"
                            }
                        };
                        _logger.LogInformation("expiration-items-details-template route generated failure response {0}", result.ToJson());
                        return result;
                    }
                    var cacheOptions = new MemoryCacheEntryOptions();
                    cacheOptions.SetAbsoluteExpiration(TimeSpan.FromMinutes(_cacheOptions.Value.ExpirationInMinutes));

                    _cache.Set(cacheKey, response, cacheOptions); result = new ApiResponse<object>
                    {
                        status = ApiResponseStatus.SUCCESS,
                        code = HttpStatusCode.OK,
                        data = response
                    };
                }
                result = new ApiResponse<object>
                {
                    status = ApiResponseStatus.SUCCESS,
                    code = HttpStatusCode.OK,
                    data = response
                };
                _logger.LogInformation("expiration-items-details-template route generated success response {0}", result.ToJson());
            }
            catch (Exception ex)
            {
                result = new ApiResponse<object>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to fetch expiration items list due to internal server error."
                    }
                };
                _logger.LogError("expiration-items-details-template route generated error response {0}", result.ToJson());
            }
            return result;
        }
    }
}
