﻿using FluentAssertions;
using iRISMobileApi.Controllers;
using iRISMobileApi.Core.AppTemplate;
using iRISMobileApi.Core.Login;
using iRISMobileApi.Dtos.AppTemplate;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.Login;
using Microsoft.Extensions.Logging;
using Moq;
using System.Net;
using Xunit;

namespace iRISMobileApi.UnitTest.Controllers
{
    public class LoginControllerTest
    {
        private readonly Mock<ILogger<LoginController>> _logger;
        private readonly Mock<ILoginService> _loginService;
        private readonly Mock<ITokenService> _tokenService;
        private readonly Mock<IAppTemplateDataService> _appTemplateDataService;
        private readonly LoginController _loginController;

        public LoginControllerTest() 
        {
            _logger = new Mock<ILogger<LoginController>>();
            _loginService = new Mock<ILoginService>();
            _tokenService = new Mock<ITokenService>();
            _appTemplateDataService = new Mock<IAppTemplateDataService>();
            _loginController = new LoginController(_logger.Object, _loginService.Object, _tokenService.Object, _appTemplateDataService.Object);
        }

        [Fact]
        public void Login_ok_response()
        {
            //arrange
            LoginRequest request = new LoginRequest
            {
                device = new Device { name = "Unit Testing Suite", type = "Desktop", os = "Windows", 
                    version = "10", id = "00-B0-D0-63-C2-26", 
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 } },
                timeInfo = new TimeInfo { localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"), 
                    timeZone = "Eastern Standard Time" },
                requestingApp = new RequestingApp { name = "Xunit", version = "1.0.0.0" },
                userInfo = new LoginRequestModel { username = "admin", password = "12345" }
            };

            UserDetails userDetails = new UserDetails { id = 0, sessionId = "0_" + DateTime.Now.ToLongTimeString(), displayName = "ABC DEF", email = "<EMAIL>", role = "Admin" };
            string token = "1234385098234fdsnfkjn2398ouwqeoc8923ndnnewf9889h932r=";
            List<WorkFlowDetails> workFlowDetailsList = new List<WorkFlowDetails> {
                new WorkFlowDetails { action = "fetchProducts" },
                new WorkFlowDetails { action = "expiredItems" }
            };
            List<AppTemplateDataResponse> appTemplateDataResponses = new List<AppTemplateDataResponse> {
                new AppTemplateDataResponse { AppScreenName = "Home Page" },
                new AppTemplateDataResponse { AppScreenName = "Products Search" },
                new AppTemplateDataResponse { AppScreenName = "Expired Items" }
            };
            _loginService.Setup(x => x.AuthenticateUser(request)).ReturnsAsync(userDetails);
            _tokenService.Setup(x => x.BuildToken(userDetails)).Returns(token);
            _loginService.Setup(x => x.FetchAppScreensByUser("admin")).ReturnsAsync(workFlowDetailsList);
            _appTemplateDataService.Setup(x => x.GetAppTemplateData(null)).ReturnsAsync(appTemplateDataResponses);

            //act
            var response = _loginController.Login(request).Result;
            ApiResponse<LoginResponse> loginReponse = response.Value;

            //assert
            loginReponse.status.Should().Be(ApiResponseStatus.SUCCESS);
            loginReponse.data.token.Should().Be(token);
            loginReponse.data.appTemplates.Count.Equals(3);
            loginReponse.error.Should().Be(null);
        }

        [Fact]
        public void Login_invalid_request_response()
        {
            //arrange
            //manually adding error that would cause `ModelState.IsValid` to be false
            _loginController.ModelState.AddModelError("userInfo", "Missing");
            LoginRequest request = new LoginRequest
            {
                device = new Device { name = "Unit Testing Suite", type = "Desktop", os = "Windows", version = "10", id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "1.0.0.0" }
            };

            Error mockError = new Error
            {
                message = "Invalid api request for login",
                details = "mandatory fields are missing from the request."
            };

            //act
            var response = _loginController.Login(request).Result;
            ApiResponse<LoginResponse> loginReponse = response.Value;

            //assert
            loginReponse.status.Should().Be(ApiResponseStatus.FAIL);
            loginReponse.code.Should().Be(HttpStatusCode.BadRequest);
            loginReponse.error.message.Should().Be(mockError.message);
        }

        [Fact]
        public void Login_forbidden_response()
        {
            //arrange
            LoginRequest request = new LoginRequest
            {
                device = new Device { name = "Mobile", os = "IOS", version = "8.3.0.6", id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 } },
                timeInfo = new TimeInfo { localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time" },
                requestingApp = new RequestingApp { name = "Xunit", version = "1.0.0.0" },
                userInfo = new LoginRequestModel { username = "admin_not_available", password = "12345" }
            };
            _loginService.Setup(x => x.AuthenticateUser(request)).Returns(Task.FromResult<UserDetails>(null));
            Error mockError = new Error
            {
                message = "Invalid username or password",
                details = "Unable to authenticate the user with given username and password"
            };

            //act
            var response = _loginController.Login(request).Result;
            ApiResponse<LoginResponse> loginReponse = response.Value;

            //assert
            loginReponse.status.Should().Be(ApiResponseStatus.FAIL);
            loginReponse.code.Should().Be(HttpStatusCode.Forbidden);
            loginReponse.error.message.Should().Be(mockError.message);
        }

        [Fact]
        public void Login_internal_server_error_response()
        {
            //arrange
            LoginRequest request = new LoginRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "1.0.0.0" },
                userInfo = new LoginRequestModel { username = "admin", password = "12345" }
            };

            var expectedException = new Exception("Internal server error occured");
            _loginService
               .Setup(svc => svc.AuthenticateUser(request))
               .Throws(expectedException);

            //act
            var response = _loginController.Login(request).Result;
            ApiResponse<LoginResponse> loginReponse = response.Value;

            //assert
            loginReponse.status.Should().Be(ApiResponseStatus.ERROR);
            loginReponse.code.Should().Be(HttpStatusCode.InternalServerError);
            loginReponse.error.message.Should().Be("Internal server error occured");
        }
    }
}