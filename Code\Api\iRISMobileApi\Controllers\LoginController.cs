﻿using iRISMobileApi.Core.AppTemplate;
using iRISMobileApi.Core.Login;
using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.Login;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Net;
using System.Diagnostics.Tracing;
using System.Diagnostics;

namespace iRISMobileApi.Controllers
{
    [Authorize]
    [ApiController]
    [Produces("application/json")]
    public class LoginController : ControllerBase
    {
        private readonly ILogger<LoginController> _logger;
        private readonly ILoginService _loginService;
        private readonly ITokenService _tokenService;
        private readonly IAppTemplateDataService _appTemplateDataService;
        private string JWTToken = null;

        public LoginController(ILogger<LoginController> logger, ILoginService loginService, ITokenService tokenService, IAppTemplateDataService appTemplateDataService)
        {
            _logger = logger;
            _loginService = loginService;
            _tokenService = tokenService;
            _appTemplateDataService = appTemplateDataService;
        }

        [AllowAnonymous]
        [Route("login"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<LoginResponse>>> Login([FromBody] LoginRequest loginRequest)
        {
            Console.WriteLine("Login Route is invoked::");
            Trace.TraceInformation("Login route is invoked:::");
            _logger.LogInformation("login route is invoked");
            ApiResponse<LoginResponse> result;
            try
            {
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<LoginResponse>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for login",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("login route generated failure response {0}", result.ToJson());
                    return result;
                }
                var userDetails = await _loginService.AuthenticateUser(loginRequest);
                if (userDetails == null)
                {
                    result = new ApiResponse<LoginResponse>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.Forbidden,
                        error = new Error
                        {
                            message = "Invalid username or password",
                            details = "Unable to authenticate the user with given username and password"
                        }
                    };
                    _logger.LogInformation("login route generated failure response {0}", result.ToJson());
                    return result;
                }
                JWTToken = _tokenService.BuildToken(userDetails);
                if (JWTToken != null)
                {
                    var userWorkFlowList = await _loginService.FetchAppScreensByUser(loginRequest.userInfo.username);
                    if (userWorkFlowList == null)
                    {
                        result = new ApiResponse<LoginResponse>
                        {
                            status = ApiResponseStatus.FAIL,
                            code = HttpStatusCode.Forbidden,
                            error = new Error
                            {
                                message = "Empty Work Flows",
                                details = "Unable to fetch work flows for user with given username"
                            }
                        };
                        _logger.LogInformation("login route generated failure response {0}", result.ToJson());
                        return result;
                    }
                    var appTemplateDataList = await _appTemplateDataService.GetAppTemplateData(null);
                    if (appTemplateDataList == null)
                    {
                        result = new ApiResponse<LoginResponse>
                        {
                            status = ApiResponseStatus.FAIL,
                            code = HttpStatusCode.Forbidden,
                            error = new Error
                            {
                                message = "Empty Template Data",
                                details = "Unable to fetch template data for user with given username"
                            }
                        };
                        _logger.LogInformation("login route generated failure response {0}", result.ToJson());
                        return result;
                    }

                    //HttpContext.Session.SetString("Token", JWTToken);                    
                    result = new ApiResponse<LoginResponse>
                    {
                        status = ApiResponseStatus.SUCCESS,
                        code = HttpStatusCode.OK,
                        data = new LoginResponse
                        {
                            token = JWTToken,
                            user = userDetails,
                            workflows = userWorkFlowList,
                            appTemplates = appTemplateDataList
                        }
                    };
                    _logger.LogInformation("login route generated success response {0}", result.ToJson());
                    Trace.TraceInformation("Login route  generated success response {0}:::", result.ToJson());
                    return result;
                }
                result = new ApiResponse<LoginResponse>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to authenticate the user due to internal server error."
                    }
                };
                _logger.LogError("login route generated error response {0}", result.ToJson());
                return result;
            }
            catch (Exception ex)
            {
                result = new ApiResponse<LoginResponse>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to authenticate the user due to internal server error."
                    }
                };
                _logger.LogError("An internal server error occured, {0}", ex.Message);
                _logger.LogError("login route generated error response {0}", result.ToJson());
                return result;
            }
        }
    }
}
