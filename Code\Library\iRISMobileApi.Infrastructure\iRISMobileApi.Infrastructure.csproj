﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
	<ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>preview</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.0.123" />
    <PackageReference Include="iRISEncrypt" Version="1.0.2" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.3" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Api\iRISMobileApi.Dtos\iRISMobileApi.Dtos.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Data\Repositories\Alerts\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Data\Repositories\Alerts\" />
  </ItemGroup>
</Project>
