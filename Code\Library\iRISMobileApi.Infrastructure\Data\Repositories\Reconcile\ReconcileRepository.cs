﻿using iRISMobileApi.Dtos.Model.Configs;
using iRISMobileApi.Dtos.Reconcile;
using System.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Data;

namespace iRISMobileApi.Infrastructure.Data.Repositories.Reconcile
{
    public class ReconcileRepository : IReconcileRepository
    {
        private readonly ILogger<ReconcileRepository> _logger;
        private readonly IOptions<DBOptions> _dbOptions;
        public ReconcileRepository(ILogger<ReconcileRepository> logger, IOptions<DBOptions> dbOptions)
        {
            _logger = logger;
            _dbOptions = dbOptions;
        }

        public ReconcileResponse UploadReconcileData(ReconcileTable reconcileTable)
        {
            _logger.LogInformation("Uploading reconcile data: ", reconcileTable);
            ReconcileResponse reconcileResponse = new ReconcileResponse();
            try
            {
                using (SqlConnection sqlConn = new SqlConnection(_dbOptions.Value.IrisDBConnectionString))
                {
                    int rowsAffected;
                    string sInsertSP = "uspInsertReconcileItems";
                    string sUpdateSP = "uspUpdateReconcileItems";
                    if (reconcileTable?.TagsData?.Count > 0)
                    {
                        sqlConn.Open();
                        foreach (var Items in reconcileTable.TagsData)
                        {
                            using (SqlCommand sqlCmd = new SqlCommand(sInsertSP, sqlConn))
                            {
                                sqlCmd.CommandType = CommandType.StoredProcedure;
                                sqlCmd.Parameters.Add("@rfid", SqlDbType.VarChar).Value = Items.Tag;
                                sqlCmd.Parameters.Add("@timestamp", SqlDbType.VarChar).Value = Items.DateTime;
                                sqlCmd.Parameters.Add("@processed", SqlDbType.VarChar).Value = reconcileTable.Processed;
                                sqlCmd.Parameters.Add("@source", SqlDbType.VarChar).Value = reconcileTable.Source;
                                rowsAffected = sqlCmd.ExecuteNonQuery();
                                reconcileResponse.state = "Updated";
                            }
                        }
                        using (var command = new SqlCommand(sUpdateSP, sqlConn)
                        {
                            CommandType = CommandType.StoredProcedure
                        })
                        {
                            rowsAffected = command.ExecuteNonQuery();
                            reconcileResponse.state = "Success";
                        }
                        sqlConn.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Exception occured while uploading reconcile data.", ex.Message);
                reconcileResponse.state = "Failed";
            }
            return reconcileResponse;
        }
    }
}
