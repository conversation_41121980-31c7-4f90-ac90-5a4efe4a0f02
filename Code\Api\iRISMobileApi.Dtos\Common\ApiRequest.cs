﻿using System.ComponentModel.DataAnnotations;

namespace iRISMobileApi.Dtos.Common
{
    public class ApiRequest
    {
        [Required]
        public Device? device { get; set; }
        public TimeInfo? timeInfo { get; set; }        
        public RequestingApp? requestingApp { get; set; }
    }

    public class Device
    {
        [Required]
        public string id { get; set; }
        public string? name { get; set; }
        public string? type { get; set; }
        public string? os { get; set; }
        public string? version { get; set; }
        public string? ssid { get; set; }
        public LocationInfo? locationInfo { get; set; }
    }

    public class LocationInfo
    {
        public double? lat { get; set; }
        public double? lon { get; set; }
    }

    public class TimeInfo
    {
        public DateTime? utcTimeStamp { get; set; }
        public DateTime? localTimeStamp { get; set; }
        public string? timeZone { get; set; }
    }

    public class RequestingApp
    {
        public string? name { get; set; }
        public string? version { get; set; }
        public string? appType { get; set; }
        public bool forceRefresh { get; set; }
    }
}
