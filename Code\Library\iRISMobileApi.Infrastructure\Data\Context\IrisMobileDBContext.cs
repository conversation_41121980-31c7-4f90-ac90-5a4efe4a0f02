﻿using System.Data;
using System.Data.SqlClient;
using iRISMobileApi.Dtos.Model.Configs;
using iRISMobileApi.Infrastructure.Data.Utils;
using Microsoft.Extensions.Options;

namespace iRISMobileApi.Infrastructure.Data.Context
{
    public class IrisMobileDBContext
    {
        private readonly IOptions<DBOptions> _dbOptions;
        private readonly string _connectionString;

        public IrisMobileDBContext(IOptions<DBOptions> dbOptions)
        {
            _dbOptions = dbOptions;
            // Encript the string using below line
            //_connectionString = EncryptionUtility.EncryptSensitiveParts(_dbOptions.Value.IrisMobileDBConnectionString); 
            _connectionString = EncryptionUtility.DecryptSensitiveParts(_dbOptions.Value.IrisMobileDBConnectionString);
            //_connectionString = _dbOptions.Value.IrisMobileDBConnectionString;
        }

        public IDbConnection CreateConnection() => new SqlConnection(_connectionString);
    }
}
