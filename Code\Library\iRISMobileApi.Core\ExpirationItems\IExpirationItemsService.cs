﻿using iRISMobileApi.Dtos.ExpirationItems;

namespace iRISMobileApi.Core.ExpiredItem
{
    public interface IExpirationItemsService   
    {
        Task<ExpirationItemsResponse> GetExpirationItems(string? appType, string searchString);

        Task<List<object>> GetExpirationItemsTemplate(string? appType, Dictionary<string, string> parameters);

        Task<List<ExpirationItemDetailsResponse>> GetExpirationItemDetails(string? appType, string catalogNo);

        Task<object> GetExpirationItemDetailsTemplate(string? appType, Dictionary<string,string> detailsParameters);
    }
}
