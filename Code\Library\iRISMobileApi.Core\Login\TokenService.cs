﻿using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.Login;
using iRISMobileApi.Dtos.Model.Configs;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace iRISMobileApi.Core.Login
{
    public class TokenService : ITokenService
    {
        private readonly ILogger<TokenService> _logger;
        private readonly IOptions<TokenOptions> _tokenOptions;
        public TokenService(ILogger<TokenService> logger, IOptions<TokenOptions> tokenOptions)
        {
            _logger = logger;
            _tokenOptions = tokenOptions;
        }

        public string BuildToken(UserDetails user)
        {
            if (user.id == -1)
                return null;
            try
            {
                _logger.LogInformation($"BuildToken of TokenService is invoked");
                var claims = new[] {
                    new Claim(ClaimTypes.Name, (user.displayName)),
                    new Claim(ClaimTypes.Email, user.email),
                    new Claim(ClaimTypes.NameIdentifier,
                    Guid.NewGuid().ToString())
                };
                var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_tokenOptions.Value.Key));
                var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256Signature);
                var tokenDescriptor = new JwtSecurityToken(_tokenOptions.Value.Issuer, _tokenOptions.Value.Issuer, claims,
                    expires: DateTime.Now.AddMinutes(_tokenOptions.Value.ExpirationInMinutes), signingCredentials: credentials);
                _logger.LogInformation("Trying to build a token for Username: {0}", (user.displayName));
                return new JwtSecurityTokenHandler().WriteToken(tokenDescriptor);
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to create token for Username: {0}, due to {1}", user.ToJson(), ex.Message);
                return null;
            }
        }
        public bool ValidateToken(string token)
        {
            var mySecret = Encoding.UTF8.GetBytes(_tokenOptions.Value.Key);
            var mySecurityKey = new SymmetricSecurityKey(mySecret);
            var tokenHandler = new JwtSecurityTokenHandler();
            try
            {
                tokenHandler.ValidateToken(token,
                new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidIssuer = _tokenOptions.Value.Issuer,
                    ValidAudience = _tokenOptions.Value.Issuer,
                    IssuerSigningKey = mySecurityKey,
                }, out SecurityToken validatedToken);
            }
            catch
            {
                return false;
            }
            return true;
        }
    }
}
