﻿using System.Net;
using iRISMobileApi.Controllers;
using iRISMobileApi.Core.Alerts;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.Model.Configs;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace iRISMobileApi.UnitTest.Controllers
{
    public class AlertsControllerTest
    {
        private readonly Mock<ILogger<AlertsController>> _logger;
        private readonly Mock<IAlertsService> _alertsService;
        private readonly AlertsController _alertsController;
        private readonly IMemoryCache _cache;

        public AlertsControllerTest()
        {
            _logger = new Mock<ILogger<AlertsController>>();
            _alertsService = new Mock<IAlertsService>();

            var mockCache = new Mock<IMemoryCache>();

            var mockCacheEntry = new Mock<ICacheEntry>();
            mockCache.Setup(m => m.CreateEntry(It.IsAny<object>())).Returns(mockCacheEntry.Object);

            object outValue = null;
            mockCache.Setup(m => m.TryGetValue(It.IsAny<object>(), out outValue)).Returns(false);

            _cache = mockCache.Object;

            var cacheSettings = new CacheOptions
            {
                ExpirationScanFrequencyInMinutes = 10,
                ExpirationInMinutes = 10
            };
            var cacheOptions = new Mock<IOptions<CacheOptions>>();
            cacheOptions.Setup(opt => opt.Value).Returns(cacheSettings);
            _alertsController = new AlertsController(_logger.Object, _alertsService.Object, _cache, cacheOptions.Object);
        }

        [Fact]
        public void Fetch_Alerts_ok_response()
        {
            //arrange
            ListRequest request = new ListRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******", appType = "Supply" },
                listInfo = new ListRequestModel { parameters = new Dictionary<string, string> { } }
            };

            List<dynamic> mockResponse = new List<dynamic> {
                new {
                    alertId = 2341,
                    catNo = "082700202356",
                    description = "COONS INTERVENTIONAL WIRE GUIDE .035\" 145CM",
                    manufacturer = "COOK MEDICAL INC"
                },
                new {
                    alertId = 9746,
                    catNo = "23402342",
                    description = "COONS WIRE GUIDE .019\" 129CM",
                    manufacturer = "COOK MEDICAL INC"
                }
            };

            _alertsService
               .Setup(svc => svc.GetAlerts(request.requestingApp.appType, request.listInfo.parameters))
               .ReturnsAsync(mockResponse);

            //act
            var response = _alertsController.FetchAlerts(request).Result;
            ApiResponse<List<object>> alertsResponse = response.Value;

            //assert
            alertsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());
            alertsResponse.data.Count.Equals(2);
            alertsResponse.data.First().Equals(mockResponse.First());
        }

        [Fact]
        public void Fetch_Alerts_invalid_request_response()
        {
            //arrange
            //manually adding error that would cause `ModelState.IsValid` to be false
            _alertsController.ModelState.AddModelError("listInfo", "Missing");
            ListRequest request = new ListRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" }
            };
            Error mockError = new Error
            {
                message = "Invalid api request for fetchAlerts",
                details = "mandatory fields are missing from the request."
            };

            //act
            var response = _alertsController.FetchAlerts(request).Result;
            ApiResponse<List<object>> alertsResponse = response.Value;

            //assert
            alertsResponse.status.Equals(ApiResponseStatus.FAIL.ToString());
            alertsResponse.code.Equals(HttpStatusCode.BadRequest);
            alertsResponse.error.message.Equals(mockError.message);
        }

        [Fact]
        public void Fetch_Alerts_empty_response()
        {
            //arrange
            ListRequest request = new ListRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                listInfo = new ListRequestModel { parameters = new Dictionary<string, string>() }
            };
            List<object> mockResponse = new List<object>();
            _alertsService
               .Setup(svc => svc.GetAlerts(request.requestingApp.appType, request.listInfo.parameters))
               .ReturnsAsync(mockResponse);

            //act
            var response = _alertsController.FetchAlerts(request).Result;
            ApiResponse<List<object>> alertsResponse = response.Value;

            //assert
            alertsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());
            Assert.Null(alertsResponse.data);
        }

        [Fact]
        public void Fetch_Alerts_internal_server_error_response()
        {
            //arrange
            ListRequest request = new ListRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                listInfo = new ListRequestModel { parameters = new Dictionary<string, string>() }
            };

            var expectedException = new Exception("Internal server error occured");
            _alertsService
               .Setup(svc => svc.GetAlerts(request.requestingApp.appType, request.listInfo.parameters))
               .Throws(expectedException);

            //act
            var response = _alertsController.FetchAlerts(request).Result;
            ApiResponse<List<object>> alertsResponse = response.Value;

            //assert
            alertsResponse.status.Equals(ApiResponseStatus.ERROR);
            alertsResponse.code.Equals(HttpStatusCode.InternalServerError);
            alertsResponse.error.message.Equals("Internal server error occured");
        }

        [Fact]
        public void Fetch_AlertDetails_ok_response()
        {
            //arrange
            DetailsRequest request = new DetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                detailsInfo = new DetailsRequestModel {
                    detailsParameters = new Dictionary<string, string> { { "alertId", "123" } }
                }
            };
            object mockResponse = new List<object> {
                new {
                    Location = "MGHIRG2VEN-A",
                    Label_Qty = "Qty",
                    Label_Location = "Location :",
                    TotalQty = 1,
                    alertLocationDetails = new List<object>{
                    new  {
                        Location = "MGHIRG2VEN-A",
                        RFID = "E004010833D64391",
                        ItemStatus = "In Cabinet",
                        ScanDate = Convert.ToDateTime("2023-06-12T09:28:08"),
                        LastActivityDate = Convert.ToDateTime("2023-06-12T09:28:08.67"),
                        ExpiredDate = Convert.ToDateTime("2027-10-30T00:00:00"),
                        ScanUser = "LANDRIGAN, CHRISTOPHER",
                        Label_SerialNo = "Serial # :",
                        Label_RFID = "RFID :",
                        Label_Expiration = "Expiration :",
                        Label_ScanDate = "Scan Date :",
                        Label_ScanUser = "Scan By :",
                        Label_Status = "Status :",
                        Label_LastActivityDate = "Last Activity :"
                    } }
                }
            };
            _alertsService
               .Setup(svc => svc.GetAlertDetails(request.requestingApp.appType, request.detailsInfo.detailsParameters))
               .ReturnsAsync(mockResponse);

            //act
            var response = _alertsController.FetchAlertDetails(request).Result;
            ApiResponse<object> alertDetailsResponse = response.Value;

            //assert
            alertDetailsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());            
            alertDetailsResponse.data.Equals(mockResponse);
        }
        [Fact]
        public void Fetch_AlertDetails_invalid_request_response()
        {
            //arrange
            //manually adding error that would cause `ModelState.IsValid` to be false
            _alertsController.ModelState.AddModelError("alertInfo", "Missing");
            DetailsRequest request = new DetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" }
            };
            Error mockError = new Error
            {
                message = "Invalid api request for fetchAlertDetails",
                details = "mandatory fields are missing from the request."
            };

            //act
            var response = _alertsController.FetchAlertDetails(request).Result;
            ApiResponse<object> alertDetailsResponse = response.Value;

            //assert
            alertDetailsResponse.status.Equals(ApiResponseStatus.FAIL.ToString());
            alertDetailsResponse.code.Equals(HttpStatusCode.BadRequest);
            alertDetailsResponse.error.message.Equals(mockError.message);
        }
        [Fact]
        public void Fetch_AlertDetails_empty_response()
        {
            //arrange
            DetailsRequest request = new DetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                detailsInfo = new DetailsRequestModel
                {
                    detailsParameters = new Dictionary<string, string> { { "alertId", "0000" } }
                }
            };
            object mockResponse = new { };
            _alertsService
               .Setup(svc => svc.GetAlertDetails(request.requestingApp.appType, request.detailsInfo.detailsParameters))
               .ReturnsAsync(mockResponse);

            //act
            var response = _alertsController.FetchAlertDetails(request).Result;
            ApiResponse<object> alertDetailsResponse = response.Value;

            //assert
            alertDetailsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());
            alertDetailsResponse.data.Equals(null);
        }
        [Fact]
        public void Fetch_AlertDetails_internal_server_error_response()
        {
            //arrange
            DetailsRequest request = new DetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                detailsInfo = new DetailsRequestModel { detailsParameters = new Dictionary<string, string> { { "alertId", null } } }
            };

            var expectedException = new Exception("Internal server error occured");
            _alertsService
               .Setup(svc => svc.GetAlertDetails(request.requestingApp.appType, request.detailsInfo.detailsParameters))
               .Throws(expectedException);

            //act
            var response = _alertsController.FetchAlertDetails(request).Result;
            ApiResponse<object> alertDetailsResponse = response.Value;

            //assert
            alertDetailsResponse.status.Equals(ApiResponseStatus.ERROR);
            alertDetailsResponse.code.Equals(HttpStatusCode.InternalServerError);
            alertDetailsResponse.error.message.Equals("Internal server error occured");
        }
    }
}