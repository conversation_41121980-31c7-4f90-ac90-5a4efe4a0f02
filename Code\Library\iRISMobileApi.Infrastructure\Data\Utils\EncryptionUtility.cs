﻿
using System.Data.SqlClient;
using System.Security.Cryptography;
namespace iRISMobileApi.Infrastructure.Data.Utils
{
    public static class EncryptionUtility
    {
        private static readonly byte[] Key = new byte[32]; // Must be 32 bytes for AES-256
        private static readonly byte[] IV = new byte[16]; // Must be 16 bytes for AES

        public static string EncryptSensitiveParts(string connectionString)
        {
            // Parse the connection string to extract and encrypt the sensitive parts
            var builder = new SqlConnectionStringBuilder(connectionString);

            // Encrypt the sensitive parts
            builder.InitialCatalog = EncryptString(builder.InitialCatalog);
            builder.UserID = EncryptString(builder.UserID);
            builder.Password = EncryptString(builder.Password);

            // Return the updated connection string
            return builder.ToString();
        }

        public static string DecryptSensitiveParts(string encryptedConnectionString)
        {
            // Decrypt the encrypted parts of the connection string
            var builder = new SqlConnectionStringBuilder(encryptedConnectionString);

            // Decrypt the sensitive parts
            builder.InitialCatalog = DecryptString(builder.InitialCatalog);
            builder.UserID = DecryptString(builder.UserID);
            builder.Password = DecryptString(builder.Password);
            return builder.ToString();
        }

        private static string EncryptString(string plainText)
        {
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Key;
                aesAlg.IV = IV;
                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(plainText);
                        }
                        return Convert.ToBase64String(msEncrypt.ToArray());
                    }
                }
            }
        }

        private static string DecryptString(string cipherText)
        {
            byte[] cipherTextBytes = Convert.FromBase64String(cipherText);

            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = Key;
                aesAlg.IV = IV;
                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msDecrypt = new MemoryStream(cipherTextBytes))
                {
                    using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (StreamReader srDecrypt = new StreamReader(csDecrypt))
                        {
                            return srDecrypt.ReadToEnd();
                        }
                    }
                }
            }
        }
    }
}

