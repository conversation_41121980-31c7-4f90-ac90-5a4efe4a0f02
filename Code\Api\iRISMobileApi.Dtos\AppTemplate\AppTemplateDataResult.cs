﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iRISMobileApi.Dtos.AppTemplate
{
    public class AppTemplateDataResult
    {
        public long AppScreenSectionFieldID { get; set; }
		
		public int AppScreenSectionID { get; set; }
		
		public string? AppScreenSectionCode { get; set; } 
		
		public string? AppScreenSectionName { get; set; }
		
		public long? AppScreenID { get; set; }
		
		public string? AppScreenCode { get; set; } 
		
		public string? AppScreenName { get; set; }

		public long? ParentAppScreenID { get; set; }

        public string? ParentAppScreenCode { get; set; }

        public string? ParentAppScreenName { get; set; }

        public string? AppScreenSectionTypeCode { get; set; }
			   
		public string? AppScreenSectionType { get; set; }
		
		public int? AppScreenSectionOrder { get; set; } 
		
		public bool? AppScreenSectionEnabled { get; set; }
			   
		public bool? AppScreenSectionDetailIconEnabled { get; set; }

        public bool? AppScreenSectionSearchBarEnabled { get; set; }

        public bool? AppScreenSectionExpanderEnabled { get; set; }

        public int AppFieldID { get; set; }
			   
		public string? AppFieldCode { get; set; } 
		
		public string? AppFieldName { get; set; } 
		
		public string? AppFieldColumnName { get; set; }

		public string? AppFieldMetaDataKey { get; set; }
		
		public string? AppScreenSectionFieldLabel { get; set; }
		
		public bool? IsFieldLabelEnabled { get; set; } 
		
		public bool? IsFieldValueEnabled { get; set; }
		
		public int? FieldLabelFontSettingID { get; set; } 
		
		public string? FieldLabelFontSettingCode { get; set; }
			   
		public string? FieldLabelFontFamily { get; set; } 
		
		public string? FieldLabelFontSize { get; set; } 
		
		public string? FieldLabelFontColor { get; set; }
			   
		public string? FieldLabelFontWeight { get; set; } 
			   
		public int? FieldValueFontSettingID { get; set; }

        public string? FieldValueFontSettingCode { get; set; }

        public string? FieldValueFontFamily { get; set; }

        public string? FieldValueFontSize { get; set; }

        public string? FieldValueFontColor { get; set; }

        public string? FieldValueFontWeight { get; set; }
			   
		public bool? IsIconEnabled { get; set; } 
		
		public int? AppScreenSectionFieldRowOrder { get; set; }

        public int? AppScreenSectionFieldColumnOrder { get; set; }

        public decimal? FieldLabelWidth { get; set; } 
		
		public decimal? FieldLabelHeight { get; set; }
			   
		public decimal? FieldValueWidth { get; set; } 
		
		public decimal? FieldValueHeight { get; set; }
			   
		public bool? IsFieldValueEditable { get; set; } 
		
		public bool? IsFieldValueValidate { get; set; } 
			   
		public int? FieldValueTypeID { get; set; } 
		
		public string? FieldValueTypeCode { get; set; } 
		
		public string? FieldValueType { get; set; }

		public int? FieldValueDataTypeID { get; set; } 
		
		public string? FieldValueDataTypeCode { get; set; } 
		
		public string? FieldValueDataType { get; set; }

        public bool? IsDisplayInExpander { get; set; }

        public DateTime? EnteredDate { get; set; } 
		
		public long? EnteredBy { get; set; }
		
		public DateTime? UpdatedDate { get; set; } 
		
		public long? UpdatedBy { get; set; }
    }
}
