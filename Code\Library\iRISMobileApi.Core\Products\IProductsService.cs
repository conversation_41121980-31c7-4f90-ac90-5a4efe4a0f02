﻿using iRISMobileApi.Dtos.Products;

namespace iRISMobileApi.Core.Products
{
    public interface IProductsService
    {
        Task<ProductsResponse> GetProducts(string? appType, string searchString);

        Task<List<object>> GetProductsTemplate(string? appType, Dictionary<string, string> parameters);

        Task<ProductDetailsResponse> GetProductDetails(string? appType, int productId);

        Task<object> GetProductDetailsTemplate(string? appType, Dictionary<string,string> detailsParameters);
    }
}
