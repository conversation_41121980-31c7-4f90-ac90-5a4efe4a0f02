﻿namespace iRISMobileApi.Dtos.ExpirationItems
{
    public class ExpirationItemDetailsTemplate
    {
        public string Location { get; set; }

        public int? TotalQty { get; set; }

        public List<ExpirationItemLocationDetails> ExpirationItemLocationDetails { get; set; }

        public ExpirationItemDetailsTemplate()
        {
            ExpirationItemLocationDetails = new List<ExpirationItemLocationDetails>();
        }
    }
    public class ExpirationItemLocationDetails
    {        
        public DateTime? ExpiredDate { get; set; }
        
        public string RFID { get; set; }
        
        public string SerialNo { get; set; }
        
        public string ItemStatus { get; set; }
    }
}
