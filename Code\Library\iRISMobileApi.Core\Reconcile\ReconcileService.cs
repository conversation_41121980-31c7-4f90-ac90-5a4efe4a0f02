﻿using iRISMobileApi.Dtos.Reconcile;
using iRISMobileApi.Infrastructure.Data.Repositories.Reconcile;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace iRISMobileApi.Core.Reconcile
{
    public class ReconcileService : IReconcileService
    {
        private readonly ILogger<ReconcileService> _logger;
        private readonly IReconcileRepository _reconcileRepository;
        public ReconcileService(ILogger<ReconcileService> logger, IReconcileRepository reconcileRepository)
        {
            _logger = logger;
            _reconcileRepository = reconcileRepository;
        }
        public ReconcileResponse ProcessReconcileData(string reconcileData)
        {
            var reconcileTable = JsonSerializer.Deserialize<ReconcileTable>(reconcileData);
            var result = _reconcileRepository.UploadReconcileData(reconcileTable);
            return result;
        }
    }
}
