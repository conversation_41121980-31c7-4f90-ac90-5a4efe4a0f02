﻿using iRISMobileApi.Dtos.Login;
using iRISMobileApi.Dtos.Model.Configs;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.DirectoryServices.AccountManagement;
using System.Net.NetworkInformation;
using System.Text;

namespace iRISMobileApi.Core.Login
{
    public class ActiveDirectoryService : IActiveDirectoryService
    {
        private readonly ILogger<LoginService> _logger;
        private readonly IOptions<ActiveDirectoryOptions> _activeDirectoryOptions;
        public ActiveDirectoryService(ILogger<LoginService> logger, IOptions<ActiveDirectoryOptions> activeDirectoryOptions)
        {
            _logger = logger;
            _activeDirectoryOptions = activeDirectoryOptions;
        }
        public async Task<User> AuthenticateUser(string username, string password)
        {
            string decodedUserName = DecodeStr(username);
            string decodedPassword = DecodeStr(password);
            UserPrincipal userPrincipal;
            bool bactiveUser;
            User empDetails = new();

            if (!CheckServerStatus())
                empDetails.ErrorCode = "0";

            CheckUserStatus(decodedUserName, out userPrincipal, out bactiveUser);

            if (bactiveUser)
            {
                if (AuthenticateLDAP(decodedUserName, decodedPassword))
                {
                    empDetails = GetAuthEmployeeDetails(userPrincipal);
                }
                else
                {
                    empDetails.ErrorCode = "2";
                }
            }
            else
            {
                empDetails.ErrorCode = "1";
            }
            return empDetails;
        }
        
        public bool CheckServerStatus()
        {
            Ping pingSender = new();
            PingReply pingReply;
            string sdomainUrl = _activeDirectoryOptions.Value.DomainName;
            string sserverUrl = _activeDirectoryOptions.Value.ServerIP;
            pingReply = pingSender.Send(sdomainUrl);
            if (pingReply.Status != IPStatus.Success)
            {
                pingReply = pingSender.Send(sserverUrl);
                if (pingReply.Status == IPStatus.Success)
                {
                    return true;
                }
                return false;
            }
            else
            {
                return true;
            }
        }
        public string GetGroups(UserPrincipal puserPrincipal)
        {
            string sgroupName = null;
            foreach (GroupPrincipal groupPrincipal in puserPrincipal.GetGroups())
            {
                if (groupPrincipal.Name.Equals(_activeDirectoryOptions.Value.AdminGroupName))
                {
                    sgroupName = "Administrator";
                    break;
                }
                else if (groupPrincipal.Name.Equals(_activeDirectoryOptions.Value.UserGroupName))
                {
                    sgroupName = "UserDS";
                    break;
                }
                else
                {
                    if (_activeDirectoryOptions.Value.AllowNonGroupUsers)
                    {
                        sgroupName = groupPrincipal.Name;
                    }
                    else
                    {
                        sgroupName = null;
                    }
                }
            }
            return sgroupName;
        }

        public void CheckUserStatus(string psUserName, out UserPrincipal puserPrincipal, out bool pbActiveUser)
        {
            puserPrincipal = UserPrincipal.FindByIdentity(new PrincipalContext(ContextType.Domain, _activeDirectoryOptions.Value.DomainName), IdentityType.SamAccountName, psUserName);
            pbActiveUser = puserPrincipal.Enabled ?? false;
        }
        public bool AuthenticateLDAP(string psusername, string pspassword)
        {
            //PrincipalContext principalContext = new(ContextType.Domain, sServerIP, sDomainInfo, ContextOptions.Negotiate);
            //bool buserAuthenticated = principalContext.ValidateCredentials(psusername, pspassword);
            //return buserAuthenticated;
            PrincipalContext context = new PrincipalContext(ContextType.Domain, _activeDirectoryOptions.Value.ServerIP, _activeDirectoryOptions.Value.DomainInfo, ContextOptions.Negotiate);
            {
                bool buserAuthenticated1 = context.ValidateCredentials(psusername, pspassword);
                return buserAuthenticated1;
            }
        }
        public User GetAuthEmployeeDetails(UserPrincipal puserPrincipal)
        {
            User empDetails = new User();
            empDetails.FirstName = puserPrincipal.GivenName;
            empDetails.MiddleName = puserPrincipal.MiddleName;
            empDetails.LastName = puserPrincipal.Surname;
            empDetails.Email = puserPrincipal.EmailAddress;
            empDetails.Designation = puserPrincipal.Description;
            empDetails.UserGroup = GetGroups(puserPrincipal);
            if (empDetails.UserGroup == null)
            {
                empDetails.FirstName = null;
                empDetails.MiddleName = null;
                empDetails.LastName = null;
                empDetails.Email = null;
                empDetails.Designation = null;
                empDetails.UserGroup = null;
                empDetails.ErrorCode = "3";
            }
            return empDetails;
        }
        public string DecodeStr(string str)
        {
            return Encoding.UTF8.GetString(Convert.FromBase64String(str));
        }
    }
}
