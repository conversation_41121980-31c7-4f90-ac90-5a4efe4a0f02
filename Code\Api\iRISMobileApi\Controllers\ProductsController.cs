﻿using System.Net;
using iRISMobileApi.Core.Products;
using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.Model.Configs;
using iRISMobileApi.Dtos.Products;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

namespace iRISMobileApi.Controllers
{
    //[Authorize]
    [ApiController]
    [Produces("application/json")]
    public class ProductsController : ControllerBase
    {
        private readonly ILogger<ProductsController> _logger;
        //IOptions<TokenOptions> _tokenOptions;
        //private readonly ITokenService _tokenService;
        private readonly IProductsService _productsService;
        private readonly IMemoryCache _cache;
        IOptions<CacheOptions> _cacheOptions;

        public ProductsController(ILogger<ProductsController> logger,/*IOptions<TokenOptions> tokenOptions, ITokenService tokenService, */IProductsService productsService, IMemoryCache cache, IOptions<CacheOptions> cacheOptions)
        {
            _logger = logger;
            //_tokenOptions = tokenOptions;
            //_tokenService = tokenService;
            _productsService = productsService;
            _cache = cache;
            _cacheOptions = cacheOptions;
        }

        [Route("products"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<ProductsResponse>>> FetchProducts([FromBody]ProductsRequest productsRequest)
        {
            ApiResponse<ProductsResponse> result;
            try
            {
                _logger.LogInformation("products route is invoked");
                //string token = HttpContext.Session.GetString("Token");
                //if (token == null || !_tokenService.ValidateToken(_tokenOptions.Value.Key, _tokenOptions.Value.Issuer, token))
                //    return new ApiResponse<ProductsResponse>
                //    {
                //        Status = "fail",
                //        Code = HttpStatusCode.Forbidden,
                //        Error = new Error
                //        {
                //            Message = "Forbidden",
                //            Detail = "Unable to authenticate the user."
                //        }
                //    };
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<ProductsResponse>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for fetchProducts",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("FetchProducts route generated failure response {0}", result.ToJson());
                    return result;
                }

                ProductsResponse response;

                string cacheKey = "fetch-products";

                if (!productsRequest.requestingApp.forceRefresh && _cache.TryGetValue(cacheKey, out ProductsResponse data))
                {
                    response = data;
                }
                else
                {
                    response = await _productsService.GetProducts(productsRequest.requestingApp.appType, productsRequest.productsInfo.searchString);
                    if (response == null || response.productsList == null || response.productsList.Count < 1)
                    {
                        result = new ApiResponse<ProductsResponse>
                        {
                            status = ApiResponseStatus.FAIL,
                            code = HttpStatusCode.NoContent,
                            error = new Error
                            {
                                message = "no products found for given search characters"
                            }
                        };
                        _logger.LogInformation("products route generated failure response {0}", result.ToJson());
                        return result;
                    }

                    var cacheOptions = new MemoryCacheEntryOptions();
                    cacheOptions.SetAbsoluteExpiration(TimeSpan.FromMinutes(_cacheOptions.Value.ExpirationInMinutes));

                    _cache.Set(cacheKey, response, cacheOptions);
                }
                result = new ApiResponse<ProductsResponse>
                {
                    status = ApiResponseStatus.SUCCESS,
                    code = HttpStatusCode.OK,
                    data = response
                };
                _logger.LogInformation("products route generated success response {0}", result.ToJson());
            }
            catch(Exception ex)
            {
                result = new ApiResponse<ProductsResponse>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to fetch products list due to internal server error."
                    }
                };
                _logger.LogError("An internal server error occured, {0}", ex.Message);
                _logger.LogError("products route generated error response {0}", result.ToJson());
            }
            return result;
        }

        [Route("product-details"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<ProductDetailsResponse>>> FetchProductDetails([FromBody]ProductDetailsRequest productDetailsRequest)
        {
            ApiResponse<ProductDetailsResponse> result;
            try
            {
                _logger.LogInformation("products-details route is invoked");
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<ProductDetailsResponse>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for fetchProductDetails",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("FetchProductDetails route generated failure response {0}", result.ToJson());
                    return result;
                }

                ProductDetailsResponse response;

                string cacheKey = "fetch-product-details";

                if (!productDetailsRequest.requestingApp.forceRefresh && _cache.TryGetValue(cacheKey, out ProductDetailsResponse data))
                {
                    response = data;
                }
                else
                {
                    response = await _productsService.GetProductDetails(productDetailsRequest.requestingApp.appType, productDetailsRequest.productInfo.productId);
                    if (response == null)
                    {
                        result = new ApiResponse<ProductDetailsResponse>
                        {
                            status = ApiResponseStatus.FAIL,
                            code = HttpStatusCode.NoContent,
                            error = new Error
                            {
                                message = "no product found for given product id"
                            }
                        };
                        _logger.LogInformation("product-details route generated failure response {0}", result.ToJson());
                        return result;
                    }

                    var cacheOptions = new MemoryCacheEntryOptions();
                    cacheOptions.SetAbsoluteExpiration(TimeSpan.FromMinutes(_cacheOptions.Value.ExpirationInMinutes));

                    _cache.Set(cacheKey, response, cacheOptions);
                }
                result = new ApiResponse<ProductDetailsResponse>
                {
                    status = ApiResponseStatus.SUCCESS,
                    code = HttpStatusCode.OK,
                    data = response
                };
                _logger.LogInformation("product-details route generated success response {0}", result.ToJson());
            }
            catch (Exception ex)
            {
                result = new ApiResponse<ProductDetailsResponse>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to fetch product details due to internal server error."
                    }
                };
                _logger.LogError("product-details route generated error response {0}", result.ToJson());
            }
            return result;
        }
    }
}
