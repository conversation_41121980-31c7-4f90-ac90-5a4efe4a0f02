﻿namespace iRISMobileApi.Dtos.Products
{
    public class ProductDetailsResponse
    {
        //public List<object> productDetailsList { get; set; }

        public List<ProductDetails> productDetailsList { get; set; }

    }


    public class ProductDetails
    {
        public string Location { get; set; }

        //public string Department { get; set; }

        public int? TotalQty { get; set; }

        public string Label_Location { get; set; }

        public string Label_Qty { get; set; }

        public List<ProductLocationDetails> productLocationDetails { get; set; }

        public ProductDetails()
        {
            productLocationDetails = new List<ProductLocationDetails>();
        }

    }


    public class ProductLocationSummary
    {
        public string Location { get; set; }

        public string Department { get; set; }

        public int? TotalQty { get; set; }

        public string Label_Location { get; set; }

        public string Label_Qty { get; set; }
    }

    public class ProductLocationDetails
    {
        public string Location { get; set; }

        public string SerialNo { get; set; }

        public string RFID { get; set; }

        public string ItemStatus { get; set; }

        public DateTime? ScanDate { get; set; }

        public DateTime? LastActivityDate { get; set; }

        public DateTime? ExpiredDate { get; set; }

        public string ScanUser { get; set; }

        public string Label_SerialNo { get; set; }

        public string Label_RFID { get; set; }

        public string Label_Expiration { get; set; }

        public string Label_ScanDate { get; set; }

        public string Label_ScanUser { get; set; }

        public string Label_Status { get; set; }

        public string Label_LastActivityDate { get; set; }
    }


    public class ProductInfo
    {
        public string ProductID { get; set; }
        public string CatalogNo { get; set; }
        public string Description { get; set; }
        public string Vendor { get; set; }
    }

    public class ProductLocationInfo
    {
        public string Location { get; set; }
        public string Department { get; set; }
        public string QtyInCabinet { get; set; }
        public string QtyOnShelf { get; set; }
        public string TotalQty { get; set; }
    }
}
