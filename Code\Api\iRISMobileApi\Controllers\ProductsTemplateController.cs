﻿using System.Diagnostics;
using System.Net;
using iRISMobileApi.Core.Products;
using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.Model.Configs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

namespace iRISMobileApi.Controllers
{
    [ApiController]
    [Produces("application/json")]
    public class ProductsTemplateController : ControllerBase
    {
        private readonly ILogger<ProductsTemplateController> _logger;
        private readonly IProductsService _productsService;
        private readonly IMemoryCache _cache;
        IOptions<CacheOptions> _cacheOptions;

        public ProductsTemplateController(ILogger<ProductsTemplateController> logger, IProductsService productsService, IMemoryCache cache, IOptions<CacheOptions> cacheOptions)
        {
            _logger = logger;
            _productsService = productsService;
            _cache = cache;
            _cacheOptions = cacheOptions;
        }

        [Route("products-template"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<List<object>>>> FetchProductsTemplate([FromBody] ListRequest listRequest)
        {
            ApiResponse<List<object>> result;
            try
            {
                Trace.TraceInformation("products template route is invoked:::");
                _logger.LogInformation("products template route is invoked");
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<List<object>>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for fetchProductsTemplate",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("FetchProductsTemplate route generated failure response {0}", result.ToJson());
                    return result;
                }

                string cacheKey = "products-template";

                List<object> response;
                if (!listRequest.requestingApp.forceRefresh && _cache.TryGetValue(cacheKey, out List<object> data))
                {
                    response = data;
                }
                else
                {
                    response = await _productsService.GetProductsTemplate(listRequest.requestingApp.appType, listRequest.listInfo.parameters);
                    if (response == null || response?.Count < 1)
                    {
                        result = new ApiResponse<List<object>>
                        {
                            status = ApiResponseStatus.FAIL,
                            code = HttpStatusCode.NoContent,
                            error = new Error
                            {
                                message = "no products found for given search characters"
                            }
                        };
                        _logger.LogInformation("products route generated failure response {0}", result.ToJson());
                        return result;
                    }

                    var cacheOptions = new MemoryCacheEntryOptions();
                    cacheOptions.SetAbsoluteExpiration(TimeSpan.FromMinutes(_cacheOptions.Value.ExpirationInMinutes));

                    _cache.Set(cacheKey, response, cacheOptions); result = new ApiResponse<List<object>>
                    {
                        status = ApiResponseStatus.SUCCESS,
                        code = HttpStatusCode.OK,
                        data = response.Take(20).ToList()
                    };
                    _logger.LogInformation("products route generated failure response {0}", result.ToJson());
                    Trace.TraceInformation("products route generated failure response {0}:::", result.ToJson());
                    return result;
                }
                result = new ApiResponse<List<object>>
                {
                    status = ApiResponseStatus.SUCCESS,
                    code = HttpStatusCode.OK,
                    data = response.Take(20).ToList()
                };
                _logger.LogInformation("products route generated success response {0}", result.ToJson());
                Trace.TraceInformation("products route generated success response {0}:::", result.ToJson());
            }
            catch (Exception ex)
            {
                result = new ApiResponse<List<object>>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to fetch products list due to internal server error."
                    }
                };
                _logger.LogError("An internal server error occured, {0}", ex.Message);
                _logger.LogError("products route generated error response {0}", result.ToJson());
                Trace.TraceError("products route generated internal server error {0}:::", ex.Message);
            }
            return result;
        }

        [Route("product-details-template"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<object>>> FetchProductDetailsTemplate([FromBody]DetailsRequest detailsRequest)
        {
            ApiResponse<object> result;
            try
            {
                _logger.LogInformation("products-details route is invoked");
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<object>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for fetchProductDetailsTemplate",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("FetchProductDetailsTemplate route generated failure response {0}", result.ToJson());
                    return result;
                }
                string cacheKey = "product-details-template";

                object response;
                if (!detailsRequest.requestingApp.forceRefresh && _cache.TryGetValue(cacheKey, out object data))
                {
                    response = data;
                }
                else
                {
                    response = await _productsService.GetProductDetailsTemplate(detailsRequest.requestingApp.appType, detailsRequest.detailsInfo.detailsParameters);
                    if (response == null)
                    {
                        result = new ApiResponse<object>
                        {
                            status = ApiResponseStatus.FAIL,
                            code = HttpStatusCode.NoContent,
                            error = new Error
                            {
                                message = "no product found for given search parameters"
                            }
                        };
                        _logger.LogInformation("product-details-template route generated failure response {0}", result.ToJson());
                        return result;
                    }
                    var cacheOptions = new MemoryCacheEntryOptions();
                    cacheOptions.SetAbsoluteExpiration(TimeSpan.FromMinutes(_cacheOptions.Value.ExpirationInMinutes));

                    _cache.Set(cacheKey, response, cacheOptions); result = new ApiResponse<object>
                    {
                        status = ApiResponseStatus.SUCCESS,
                        code = HttpStatusCode.OK,
                        data = response
                    };
                }
                result = new ApiResponse<object>
                {
                    status = ApiResponseStatus.SUCCESS,
                    code = HttpStatusCode.OK,
                    data = response
                };
                _logger.LogInformation("product-details-template route generated success response {0}", result.ToJson());
            }
            catch (Exception ex)
            {
                result = new ApiResponse<object>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to fetch product details due to internal server error."
                    }
                };
                _logger.LogError("product-details-template route generated error response {0}", result.ToJson());
            }
            return result;
        }
    }
}
