﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
	<ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>preview</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Abstractions" Version="7.0.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.DirectoryServices.AccountManagement" Version="7.0.0" />
	<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Api\iRISMobileApi.Dtos\iRISMobileApi.Dtos.csproj" />
    <ProjectReference Include="..\iRISMobileApi.Infrastructure\iRISMobileApi.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Alerts\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Alerts\" />
  </ItemGroup>
</Project>
