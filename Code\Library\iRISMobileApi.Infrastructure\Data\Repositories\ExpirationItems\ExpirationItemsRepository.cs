﻿using System.Data;
using Dapper;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.ExpirationItems;
using iRISMobileApi.Infrastructure.Data.Context;
using Microsoft.Extensions.Logging;

namespace iRISMobileApi.Infrastructure.Data.Repositories.ExpiredItem
{
    public class ExpirationItemsRepository : IExpirationItemsRepository
    {
        private readonly ILogger<ExpirationItemsRepository> _logger;
        private readonly IrisMobileDBContext _dBContext;
        public ExpirationItemsRepository(ILogger<ExpirationItemsRepository> logger, IrisMobileDBContext dBContext)
        {
            _logger = logger;
            _dBContext = dBContext;
        }
        public async Task<List<object>> GetExpirationItems(string? appType, string? searchString)
        {
            List<object> expirationItemsList;
            try
            {
                _logger.LogInformation("GetExpirationItems of ExpirationItemsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                if (string.IsNullOrEmpty(searchString))
                {
                    searchString = null;
                }
                if (!string.IsNullOrEmpty(searchString))
                    _logger.LogInformation("Expiration Items with searchString: {0} are being fetched from db", searchString);
                var parameters = new DynamicParameters();
                parameters.Add("@SearchString", searchString);
                var procName = "";
                if (!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPSearchExpiredItems";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPSearchExpiredItems";
                }
                if(!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<object>(procName,
                   parameters,
                   commandType: CommandType.StoredProcedure);
                    if (!dbResult.Any())
                    {
                        _logger.LogInformation("No Expiration Items are found with searchString: {0}", searchString);
                        return null;
                    }
                    expirationItemsList = dbResult.ToList();
                    _logger.LogInformation("{0} Expiration Items are found with searchString: {1}", expirationItemsList.Count, searchString);
                } 
                else
                {
                    _logger.LogInformation("No app type associated for proc mapping in GetExpirationItems searchString: {0}", searchString);
                    return null;
                }
               
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetExpirationItems of ExpirationItemsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetExpirationItems of ExpirationItemsRepository: {ex}");
                throw;
            }
            return expirationItemsList;
        }

        public async Task<List<object>> GetExpirationItemsTemplate(string? appType, Dictionary<string, string> parameters)
        {
            List<object> dataList;
            try
            {
                _logger.LogInformation("GetExpirationItemsTemplate of ExpirationItemsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                var dynamicParams = new DynamicParameters();
                if (parameters.Count == 0)
                    dynamicParams.Add("@SearchString", "");
                else
                {
                    foreach (var keyValue in parameters)
                    {
                        dynamicParams.Add("@" + keyValue.Key, keyValue.Value);
                    }
                }
                var procName = "";
                if (!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPSearchExpiredItemsTemplate";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPSearchExpiredItemsTemplate";
                }
                if (!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<object>(procName,
                   dynamicParams, commandType: CommandType.StoredProcedure);
                    if (!dbResult.Any())
                    {
                        _logger.LogInformation("No Expiration Items are found with given params: {0}", parameters);
                        return null;
                    }
                    dataList = dbResult.ToList();
                    _logger.LogInformation("{0} Expiration Items are found with given params: {1}", dataList.Count, parameters);
                }
                else
                {
                    _logger.LogInformation("No app type associated for proc mapping in GetExpirationItemsTemplate params: {0}", parameters);
                    return null;
                }

            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetExpirationItemsTemplate of ExpirationItemsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetExpirationItemsTemplate of ExpirationItemsRepository: {ex}");
                throw;
            }
            return dataList;
        }

        public async Task<List<ExpirationItemDetailsDTO>> GetExpirationItemDetails(string? appType, string catalogNo)
        {
            List<ExpirationItemDetailsDTO> expirationItemDetailsObj;
            try
            {
                _logger.LogInformation("GetExpirationItemsDetails of ExpirationItemsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                if (catalogNo != null)
                    _logger.LogInformation("ExpirationItems with catalogNo: {0} are being fetched from db", catalogNo);
                var parameters = new DynamicParameters();
                parameters.Add("@CatalogNo", catalogNo);
                //parameters.Add("@AppType", appType);
                var procName = "";
                if (!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPExpiredItemDetails";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPExpiredItemDetails";
                }
                if(!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<ExpirationItemDetailsDTO>("uspFetchExpiredItemDetails",
                    parameters,
                    commandType: CommandType.StoredProcedure);
                    if (dbResult.Count() == 0)
                    {
                        _logger.LogInformation("No Expiration Items are found with catalogNo: {0}", catalogNo);
                        return null;
                    }
                    expirationItemDetailsObj = dbResult.ToList();
                    _logger.LogInformation("Expiration Items details are found with catalogNo: {0}", catalogNo);
                } 
                else
                {
                    _logger.LogInformation("No app type associated for proc mapping in GetExpirationItemDetails with catalogNo: {0}", catalogNo);
                    return null;
                }                
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetExpirationItemsDetails of ExpirationItemsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetExpirationItemsDetails of ExpirationItemsRepository: {ex}");
                throw;
            }
            return expirationItemDetailsObj;
        }

        public async Task<List<ExpirationItemDetailsDTO>> GetExpirationItemDetailsTemplate(string? appType, Dictionary<string, string> detailsParameters)
        {
            List<ExpirationItemDetailsDTO> expirationItemDetailsObj;
            try
            {
                _logger.LogInformation("GetExpirationItemsDetails of ExpirationItemsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                var dynamicParams = new DynamicParameters();
                if (detailsParameters.Count == 0)
                    dynamicParams.Add("@SearchString", "");
                else
                {
                    foreach (var keyValue in detailsParameters)
                    {
                        dynamicParams.Add("@" + keyValue.Key, keyValue.Value);
                    }
                }
                var procName = "";
                if (!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPExpiredItemDetails";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPExpiredItemDetails";
                }
                if (!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<ExpirationItemDetailsDTO>("uspFetchExpiredItemDetails",
                    dynamicParams,
                    commandType: CommandType.StoredProcedure);
                    if (!dbResult.Any())
                    {
                        _logger.LogInformation("No Expiration Items are found given parameters");
                        return null;
                    }
                    expirationItemDetailsObj = dbResult.ToList();
                    _logger.LogInformation("Expiration Items details are found given parameters");
                }
                else
                {
                    _logger.LogInformation("No app type associated for proc mapping in GetExpirationItemDetails");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetExpirationItemsDetails of ExpirationItemsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetExpirationItemsDetails of ExpirationItemsRepository: {ex}");
                throw;
            }
            return expirationItemDetailsObj;
        }
    }
}
