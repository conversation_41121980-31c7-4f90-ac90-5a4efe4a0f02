﻿using System.Runtime.Serialization;

namespace iRISMobileApi.Dtos.Login
{
    [Serializable]
    [DataContract]
    public class AppScreenDetails
    {
        public AppScreenDetails()
        {

        }
        [DataMember]
        public long UserId { get; set; }
        [DataMember]
        public string Username { get; set; }
        [DataMember]
        public string Email { get; set; }
        [DataMember]
        public int AppTypeId { get; set; }
        [DataMember]
        public string AppType { get; set; }
        [DataMember]
        public string AppScreenCode { get; set; }
        [DataMember]
        public string AppScreenName { get; set; }
        [DataMember]
        public int AppScreenOrder { get; set; }
        [DataMember]
        public string AppScreenGroupName { get; set; }

        public string? IconSettingCode { get; set; }

        public string IconName { get; set; }

        public string IconURL { get; set; }
    }
}
