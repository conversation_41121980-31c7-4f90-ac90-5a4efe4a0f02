﻿using System.Runtime.Serialization;

namespace iRISMobileApi.Dtos.Login
{
    [Serializable]
    [DataContract]
    public class User
    {
        public User()
        {

        }

        [DataMember]
        public int UserID { get; set; }
        [DataMember]
        public string FirstName { get; set; }
        [DataMember]
        public string MiddleName { get; set; }
        [DataMember]
        public string LastName { get; set; }
        [DataMember]
        public string Email { get; set; }
        [DataMember]
        public string RFID { get; set; }
        [DataMember]
        public string Designation { get; set; }
        [DataMember]
        public string UserGroup { get; set; }
        [DataMember]
        public string ErrorCode { get; set; }
    }
}
