﻿using iRISMobileApi.Dtos.AppTemplate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iRISMobileApi.Infrastructure.Data.Repositories.AppTemplate
{
    public interface IAppTemplateDataRepository
    {
        Task<List<AppTemplateDataResult>> GetAppTemplateData(string? appScreenName);

        Task<List<AppScreenSectionServicesDataResult>> GetAppScreenSectionServicesData();
    }
}
