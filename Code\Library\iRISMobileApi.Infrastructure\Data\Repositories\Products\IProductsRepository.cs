﻿using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Products;

namespace iRISMobileApi.Infrastructure.Data.Repositories.Products
{
    public interface IProductsRepository
    {
        Task<List<object>> GetProducts(string? appType, string searchString);

        Task<List<object>> GetProductsTemplate(string? appType, Dictionary<string, string> parameters);

        Task<List<object>> GetProductDetails(string? appType, int productId);

        Task<List<ProductLocationSummary>> GetProductLocationSummary(string? appType, int productId);

        Task<List<ProductLocationDetails>> GetProductLocationDetails(string? appType, int productId);

        Task<List<ProductLocationSummary>> GetProductLocationSummaryTemplate(string? appType, Dictionary<string, string> detailsParameters);

        Task<List<ProductLocationDetails>> GetProductLocationDetailsTemplate(string? appType, Dictionary<string, string> detailsParameters);
    }
}
