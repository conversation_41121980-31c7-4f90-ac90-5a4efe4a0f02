﻿using iRISMobileApi.Core.Alerts;
using iRISMobileApi.Core.AppTemplate;
using iRISMobileApi.Core.ExpiredItem;
using iRISMobileApi.Core.Login;
using iRISMobileApi.Core.Products;
using iRISMobileApi.Core.Reconcile;
using iRISMobileApi.Dtos.Model.Configs;
using iRISMobileApi.Infrastructure.Data.Context;
using iRISMobileApi.Infrastructure.Data.Repositories.Alerts;
using iRISMobileApi.Infrastructure.Data.Repositories.AppTemplate;
using iRISMobileApi.Infrastructure.Data.Repositories.ExpiredItem;
using iRISMobileApi.Infrastructure.Data.Repositories.Login;
using iRISMobileApi.Infrastructure.Data.Repositories.Products;
using iRISMobileApi.Infrastructure.Data.Repositories.Reconcile;

namespace iRISMobileApi.Configuration
{
    public static class DependencyConfiguration
    {
        public static IServiceCollection RegisterServices(this IServiceCollection services, IConfiguration configuration)
        {
            services
                .AddSingleton<IrisMobileDBContext>()
                .AddTransient<ITokenService, TokenService>()
                .AddTransient<IActiveDirectoryService, ActiveDirectoryService>()
                .AddTransient<ILoginService, LoginService>()
                .AddTransient<ILoginRepository, LoginRepository>()
                .AddTransient<IProductsService, ProductsService>()
                .AddTransient<IProductsRepository, ProductsRepository>()
                .AddTransient<IExpirationItemsService, ExpirationItemsService>()
                .AddTransient<IExpirationItemsRepository, ExpirationItemsRepository>()                
                .AddTransient<IReconcileService, ReconcileService>()
                .AddTransient<IReconcileRepository, ReconcileRepository>()
                .AddTransient<IAppTemplateDataService, AppTemplateDataService>()
                .AddTransient<IAppTemplateDataRepository, AppTemplateDataRepository>()
                .AddTransient<IAlertsService, AlertsService>()
                .AddTransient<IAlertsRepository, AlertsRepository>();
            return services;
        }

        public static IServiceCollection RegisterOptions(this IServiceCollection services, IConfiguration configuration)
        {
            return services
                    .AddOptions()
                    .Configure<DBOptions>(configuration.GetSection("DBConfigs"))
                    .Configure<TokenOptions>(configuration.GetSection("Jwt"))
                    .Configure<ActiveDirectoryOptions>(configuration.GetSection("ActiveDirectory"))
                    .Configure<CacheOptions>(configuration.GetSection("Cache"));
        }
    }
}
