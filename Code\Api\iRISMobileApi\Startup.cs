using iRISMobileApi.Configuration;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.Model.Configs;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Serilog;
using Serilog.Events;
using System.Net;
using System.Text;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace iRISMobileApi
{
    public class Startup
    {
        public IConfiguration _configuration { get; set; }
        
        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            var builder = new ConfigurationBuilder();
            builder.SetBasePath(Directory.GetCurrentDirectory())
                   .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            _configuration = builder.Build();

            ILogger logger = new LoggerFactory().AddSerilog().CreateLogger("iRISMobileApi.Logger");
            services.AddSingleton(logger);

            services.AddSession();
            // register api options from configurations
            services.RegisterOptions(_configuration);
            // register all api dependencies
            services.RegisterServices(_configuration);
            var cacheOptions = services.BuildServiceProvider().GetService<IOptions<CacheOptions>>().Value;
            services.AddMemoryCache(options =>
            {
                // Set cache size limit (in bytes)
                //options.SizeLimit = 1024 * 1024 * 100; // 100 MB

                // Set cache compaction percentage
                //options.CompactionPercentage = 0.25; // 25%

                // Set cache expiration scan frequency
                options.ExpirationScanFrequency = TimeSpan.FromMinutes(cacheOptions.ExpirationScanFrequencyInMinutes);
            });
            services.AddMvc().AddNewtonsoftJson();
            services.AddControllers().AddJsonOptions(options => 
            { 
                options.JsonSerializerOptions.PropertyNamingPolicy = null; 
            });

            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "iRISMobileApi", Version = "v1" });
            });

            #region JWT bearer for authentication
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddJwtBearer(options =>
            {
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidateLifetime = true,
                    ValidateIssuerSigningKey = true,
                    ValidIssuer = _configuration["Jwt:Issuer"],
                    ValidAudience = _configuration["Jwt:Issuer"], //_configuration["Jwt:Audience"]
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]))
                };
            });
            #endregion

            #region Model Validation Response
            services.Configure<ApiBehaviorOptions>(apiBehaviorOptions =>
                apiBehaviorOptions.InvalidModelStateResponseFactory = actionContext => {
                    return new BadRequestObjectResult(new
                    {                        
                        status = ApiResponseStatus.ERROR,
                        code = HttpStatusCode.BadRequest,
                        error = new
                        {
                            message = "one or more validation error occured.",
                            details = string.Join(", ", actionContext.ModelState.Values.SelectMany(x => x.Errors).Select(x => x.ErrorMessage))
                        }
                    });
                }
            );
            #endregion

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            #region serilog logging
            app.UseSerilogRequestLogging(options =>
            {
                options.GetLevel = (ctx, elapsed, ex) =>
                {
                    if (ex != null || ctx.Response.StatusCode > 499)
                        return LogEventLevel.Error;
                    return elapsed > 3000 ? LogEventLevel.Warning : LogEventLevel.Information;
                };
            });
            #endregion

            //app.UseSession();

            //app.Use(async (context, next) =>
            //{
            //    var token = context.Session.GetString("Token");
            //    if (!string.IsNullOrEmpty(token))
            //    {
            //        context.Request.Headers.Add("Authorization", "Bearer " + token);
            //    }
            //    await next();
            //});

            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "iRISMobileApi v1"));

            app.UseExceptionHandler(a => a.Run(async context =>
            {
                var exceptionHandlerPathFeature = context.Features.Get<IExceptionHandlerPathFeature>();
                var exception = exceptionHandlerPathFeature.Error;

                await context.Response.WriteAsJsonAsync(new {
                    error = exception.Message 
                });
            }));

            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
