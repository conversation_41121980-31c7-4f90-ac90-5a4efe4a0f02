﻿using System.Data;
using Dapper;
using iRISMobileApi.Infrastructure.Data.Context;
using Microsoft.Extensions.Logging;

namespace iRISMobileApi.Infrastructure.Data.Repositories.Alerts
{
    public class AlertsRepository : IAlertsRepository
    {
        private readonly ILogger<AlertsRepository> _logger;
        private readonly IrisMobileDBContext _dBContext;
        public AlertsRepository(ILogger<AlertsRepository> logger, IrisMobileDBContext dBContext)
        {
            _logger = logger;
            _dBContext = dBContext;
        }

        public async Task<List<object>> GetAlerts(string? appType, Dictionary<string, string> parameters)
        {
            List<object> dataList;
            try
            {
                _logger.LogInformation("GetAlerts of AlertsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                
                var dynamicParams = new DynamicParameters();
                if (parameters.Count == 0)
                    dynamicParams.Add("@SearchString", "");
                else
                {
                    foreach (var keyValue in parameters)
                    {
                        dynamicParams.Add("@" + keyValue.Key, keyValue.Value);
                    }
                }
                var procName = "uspSPCAlerts";
                var dbResult = await _dBContext.CreateConnection().QueryAsync<object>(procName,
                    dynamicParams,
                    commandType: CommandType.StoredProcedure);
                if (!dbResult.Any())
                {
                    _logger.LogInformation("No alert is found with given params: {0}", parameters);
                    return null;
                }
                dataList = dbResult.ToList();
                _logger.LogInformation("{0} alerts are found with given params: {1}", dataList.Count, parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetAlerts of AlertsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetAlerts of AlertsRepository: {ex}");
                throw;
            }
            return dataList;
        }
                
        public async Task<object> GetAlertDetails(string? appType, Dictionary<string, string> detailsParameters)
        {
            try
            {
                _logger.LogInformation("GetAlertDetails of AlertsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                var dynamicParams = new DynamicParameters();
                if (detailsParameters.Count == 0)
                    dynamicParams.Add("@SearchString", "");
                else
                {
                    foreach (var keyValue in detailsParameters)
                    {
                        dynamicParams.Add("@" + keyValue.Key, keyValue.Value);
                    }
                }
                var procName = "uspSPCAlertDetails";
                var dbResult = await _dBContext.CreateConnection().QueryAsync<object>(procName,
                dynamicParams,
                commandType: CommandType.StoredProcedure);
                if (!dbResult.Any())
                {
                    _logger.LogInformation("No alert is found for given parameters");
                    return null;
                }
                _logger.LogInformation("Alert details are found for given parameters");
                return dbResult;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetAlertDetails of AlertsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetAlertDetails of AlertsRepository: {ex}");
                throw;
            }
        }
    }
}
