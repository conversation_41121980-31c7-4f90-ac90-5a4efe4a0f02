﻿namespace iRISMobileApi.Dtos.Model.Configs
{
    public class ActiveDirectoryOptions
    {
        public bool IsEnabled { get; set; }
        public string ServerIP { get; set; }
        public string DomainInfo { get; set; }
        public string DomainName { get; set; }
        public string AdminGroupName { get; set; }
        public string UserGroupName { get; set; }
        public bool AllowNonGroupUsers { get; set; }
    }
}
