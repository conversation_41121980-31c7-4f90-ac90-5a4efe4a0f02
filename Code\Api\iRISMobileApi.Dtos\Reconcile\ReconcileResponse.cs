﻿namespace iRISMobileApi.Dtos.Reconcile
{
    public class ReconcileResponse
    {
        public string state { get; set; }
    }
    public class ProductInfoItemForReceiveTissue
    {
        public string productId { get; set; }
        public string catNo { get; set; }
        public string description1 { get; set; }
        public string manufacturer { get; set; }

        public string ndc { get; set; }
        public string brandName { get; set; }
        public string description2 { get; set; }
        public string description3 { get; set; }

        public string size { get; set; }
        public string material { get; set; }
        public string refPrice { get; set; }
        public string picture { get; set; }

        public string skuNo { get; set; }
        public string ownership { get; set; }
        public string atatus { get; set; }
        public string productTypeId { get; set; }

        public string pcdm { get; set; }
        public string mmid { get; set; }
        public string productCategoryId { get; set; }
        public string productSubCategoryId { get; set; }

        public string productGroupID { get; set; }

        public string poNumber { get; set; }
        public string storageLocation { get; set; }
        public string implantBatchNo { get; set; }

        public string productType { get; set; }
        public string productUnitType { get; set; }
        public string productCategory { get; set; }

        public string chargeCode { get; set; }
        public string productSubCategory { get; set; }
        public string productGroup { get; set; }

        public string referencePrice { get; set; }
        public string minorId { get; set; }
        public string majorId { get; set; }
        public string vendorNumber { get; set; }

    }
}
