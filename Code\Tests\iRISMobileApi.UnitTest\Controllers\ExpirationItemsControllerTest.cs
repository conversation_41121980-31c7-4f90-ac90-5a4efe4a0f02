﻿using FluentAssertions;
using iRISMobileApi.Controllers;
using iRISMobileApi.Core.ExpiredItem;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.ExpirationItems;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Moq;
using System.Net;
using Xunit;

namespace iRISMobileApi.UnitTest.Controllers
{
    public class ExpirationItemsControllerTest
    {
        private readonly Mock<ILogger<ExpirationItemsController>> _logger;
        private readonly Mock<IExpirationItemsService> _ExpirationItemsService;
        private readonly ExpirationItemsController _ExpirationItemsController;
        public ExpirationItemsControllerTest()
        {
            _logger = new Mock<ILogger<ExpirationItemsController>>();
            _ExpirationItemsService = new Mock<IExpirationItemsService>();
            _ExpirationItemsController = new ExpirationItemsController(_logger.Object, _ExpirationItemsService.Object);
        }
        [Fact]
        public void Fetch_ExpirationItems_ok_response()
        {
            //arrange
            ExpirationItemsRequest request = new ExpirationItemsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******", appType = "Supply" },
                expirationItemsInfo = new ExpirationItemsRequestModel { searchString = "" }
            };
            ExpirationItemsResponse mockResponse = new ExpirationItemsResponse
            {
                expirationItemsList = new List<dynamic> {
                    new { expirationItemId = 2341,
                        catNo = "082700202356",
                        description = "COONS INTERVENTIONAL WIRE GUIDE .035\" 145CM",
                        manufacturer = "COOK MEDICAL INC"
                    },
                    new { expirationItemId = 9746,
                        catNo = "23402342",
                        description = "COONS WIRE GUIDE .019\" 129CM",
                        manufacturer = "COOK MEDICAL INC"
                    }
                }
            };
            _ExpirationItemsService
               .Setup(svc => svc.GetExpirationItems(request.requestingApp.appType, request.expirationItemsInfo.searchString))
               .ReturnsAsync(mockResponse);

            //act
            var response = _ExpirationItemsController.FetchExpirationItems(request).Result;
            ApiResponse<ExpirationItemsResponse> ExpirationItemsResponse = response.Value;

            //assert
            ExpirationItemsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());
            ExpirationItemsResponse.data.expirationItemsList.Count.Equals(2);
            ExpirationItemsResponse.data.expirationItemsList[0].Equals(mockResponse.expirationItemsList[0]);
        }
        [Fact]
        public void Fetch_ExpirationItems_invalid_request_response()
        {
            //arrange
            //manually adding error that would cause `ModelState.IsValid` to be false
            _ExpirationItemsController.ModelState.AddModelError("ExpirationItemsInfo", "Missing");
            ExpirationItemsRequest request = new ExpirationItemsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" }
            };
            Error mockError = new Error
            {
                message = "Invalid api request for fetchExpirationItems",
                details = "mandatory fields are missing from the request."
            };

            //act
            var response = _ExpirationItemsController.FetchExpirationItems(request).Result;
            ApiResponse<ExpirationItemsResponse> ExpirationItemsResponse = response.Value;

            //assert
            ExpirationItemsResponse.status.Equals(ApiResponseStatus.FAIL.ToString());
            ExpirationItemsResponse.code.Should().Be(HttpStatusCode.BadRequest);
            ExpirationItemsResponse.error.message.Should().Be(mockError.message);
        }
        [Fact]
        public void Fetch_ExpirationItems_empty_response()
        {
            //arrange
            ExpirationItemsRequest request = new ExpirationItemsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                expirationItemsInfo = new ExpirationItemsRequestModel { searchString = "123" }
            };
            ExpirationItemsResponse mockResponse = new ExpirationItemsResponse();
            _ExpirationItemsService
               .Setup(svc => svc.GetExpirationItems(request.requestingApp.appType, request.expirationItemsInfo.searchString))
               .ReturnsAsync(mockResponse);

            //act
            var response = _ExpirationItemsController.FetchExpirationItems(request).Result;
            ApiResponse<ExpirationItemsResponse> ExpirationItemsResponse = response.Value;

            //assert
            ExpirationItemsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());
            ExpirationItemsResponse.data.expirationItemsList.IsNullOrEmpty();
        }
        [Fact]
        public void Fetch_ExpirationItems_internal_server_error_response()
        {
            //arrange
            ExpirationItemsRequest request = new ExpirationItemsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                expirationItemsInfo = new ExpirationItemsRequestModel { searchString = "" }
            };

            var expectedException = new Exception("Internal server error occured");
            _ExpirationItemsService
               .Setup(svc => svc.GetExpirationItems(request.requestingApp.appType, request.expirationItemsInfo.searchString))
               .Throws(expectedException);

            //act
            var response = _ExpirationItemsController.FetchExpirationItems(request).Result;
            ApiResponse<ExpirationItemsResponse> ExpirationItemsResponse = response.Value;

            //assert
            ExpirationItemsResponse.status.Should().Be(ApiResponseStatus.ERROR);
            ExpirationItemsResponse.code.Should().Be(HttpStatusCode.InternalServerError);
            ExpirationItemsResponse.error.message.Should().Be("Internal server error occured");
        }
        [Fact]
        public void Fetch_ExpirationItemDetails_ok_response()
        {
            //arrange
            ExpirationItemDetailsRequest request = new ExpirationItemDetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                expirationItemInfo = new ExpirationItemDetailsRequestModel { catalogNo = "1432" }
            };
            List<ExpirationItemDetailsResponse> mockResponse = new List<ExpirationItemDetailsResponse> {
                new ExpirationItemDetailsResponse (
                    "Location 1",
                    new List<ExpirationItemDetailsModel>{
                        new ExpirationItemDetailsModel {
                            CatalogNo = "1234685"
                        } }, 1) };
            _ExpirationItemsService
               .Setup(svc => svc.GetExpirationItemDetails(request.requestingApp.appType, request.expirationItemInfo.catalogNo))
               .ReturnsAsync(mockResponse);

            //act
            var response = _ExpirationItemsController.FetchExpirationItemDetails(request).Result;
            ApiResponse<List<ExpirationItemDetailsResponse>> ExpirationItemDetailsResponse = response.Value;

            //assert
            ExpirationItemDetailsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());
            ExpirationItemDetailsResponse.data.Count.Equals(1);
            ExpirationItemDetailsResponse.data[0].Equals(mockResponse[0]);
        }
        [Fact]
        public void Fetch_ExpirationItemDetails_invalid_request_response()
        {
            //arrange
            //manually adding error that would cause `ModelState.IsValid` to be false
            _ExpirationItemsController.ModelState.AddModelError("expirationItemInfo", "Missing");
            ExpirationItemDetailsRequest request = new ExpirationItemDetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" }
            };
            Error mockError = new Error
            {
                message = "Invalid api request for fetchExpirationItemDetails",
                details = "mandatory fields are missing from the request."
            };

            //act
            var response = _ExpirationItemsController.FetchExpirationItemDetails(request).Result;
            ApiResponse<List<ExpirationItemDetailsResponse>> ExpirationItemDetailsResponse = response.Value;

            //assert
            ExpirationItemDetailsResponse.status.Equals(ApiResponseStatus.FAIL.ToString());
            ExpirationItemDetailsResponse.code.Should().Be(HttpStatusCode.BadRequest);
            ExpirationItemDetailsResponse.error.message.Should().Be(mockError.message);
        }
        [Fact]
        public void Fetch_ExpirationItemDetails_empty_response()
        {
            //arrange
            ExpirationItemDetailsRequest request = new ExpirationItemDetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                expirationItemInfo = new ExpirationItemDetailsRequestModel { catalogNo = "123" }
            };
            List<ExpirationItemDetailsResponse> mockResponse = null;
            _ExpirationItemsService
               .Setup(svc => svc.GetExpirationItemDetails(request.requestingApp.appType, request.expirationItemInfo.catalogNo))
               .ReturnsAsync(mockResponse);

            //act
            var response = _ExpirationItemsController.FetchExpirationItemDetails(request).Result;
            ApiResponse<List<ExpirationItemDetailsResponse>> ExpirationItemDetailsResponse = response.Value;

            //assert
            ExpirationItemDetailsResponse.status.Equals(ApiResponseStatus.SUCCESS.ToString());
            ExpirationItemDetailsResponse.data.IsNullOrEmpty();
        }
        [Fact]
        public void Fetch_ExpirationItemDetails_internal_server_error_response()
        {
            //arrange
            ExpirationItemDetailsRequest request = new ExpirationItemDetailsRequest
            {
                device = new Device
                {
                    name = "Unit Testing Suite",
                    type = "Desktop",
                    os = "Windows",
                    version = "10",
                    id = "00-B0-D0-63-C2-26",
                    locationInfo = new LocationInfo { lat = 40.098, lon = -39.876 }
                },
                timeInfo = new TimeInfo
                {
                    localTimeStamp = DateTime.Parse("2023-10-17 17:26:00.000"),
                    utcTimeStamp = DateTime.Parse("2023-10-18 00:26:00.000"),
                    timeZone = "Eastern Standard Time"
                },
                requestingApp = new RequestingApp { name = "Xunit", version = "*******" },
                expirationItemInfo = new ExpirationItemDetailsRequestModel { catalogNo = "1252" }
            };

            var expectedException = new Exception("Internal server error occured");
            _ExpirationItemsService
               .Setup(svc => svc.GetExpirationItemDetails(request.requestingApp.appType, request.expirationItemInfo.catalogNo))
               .Throws(expectedException);

            //act
            var response = _ExpirationItemsController.FetchExpirationItemDetails(request).Result;
            ApiResponse<List<ExpirationItemDetailsResponse>> ExpirationItemDetailsResponse = response.Value;

            //assert
            ExpirationItemDetailsResponse.status.Should().Be(ApiResponseStatus.ERROR);
            ExpirationItemDetailsResponse.code.Should().Be(HttpStatusCode.InternalServerError);
            ExpirationItemDetailsResponse.error.message.Should().Be("Internal server error occured");
        }
    }
}