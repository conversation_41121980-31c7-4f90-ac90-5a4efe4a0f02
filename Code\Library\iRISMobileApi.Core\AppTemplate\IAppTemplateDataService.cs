﻿using iRISMobileApi.Dtos.AppTemplate;
using iRISMobileApi.Dtos.Products;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iRISMobileApi.Core.AppTemplate
{
    public interface IAppTemplateDataService
    {
        Task<List<AppTemplateDataResponse>> GetAppTemplateData(string? appScreenName);

        Task<List<AppScreenSectionServicesDataResult>> GetAppScreenSectionServicesData();
    }
}
