{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Jwt": {"Key": "Mobile Aspects solutions for operational efficiency and patient safety are used by the top hospitals in the world and community hospitals alike.", "Issuer": "Mobile Aspects", "Audience": "https://devspcirismobileservices.azurewebsites.net/", "ExpirationInMinutes": 30}, "AllowedHosts": "*", "DBConfigs": {"IrisMobileDBConnectionString": "Data Source=ai-dtl-svr04.eastus.cloudapp.azure.com;Initial Catalog=\"AFWdseJnPMAzKJ65KS2Rz9jXBcx0V9vOInpX2GlENPE=\";User ID=\"s70a6dGr0gThelG0LIa3Gw==\";Password=\"1KKBoIOutCEDmsrAj2WtxQ==\""}, "ActiveDirectory": {"IsEnabled": false, "ServerIP": "***************", "DomainInfo": "OU=CompanyUsers,DC=maspects,DC=com", "DomainName": "maspects.com", "AdminGroupName": "Supply Admin", "UserGroupName": "Supply User", "AllowNonGroupUsers": true}, "Cache": {"ExpirationScanFrequencyInMinutes": 10, "ExpirationInMinutes": 1140}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/log.txt", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}