﻿using iRISMobileApi.Core.Utils;
using iRISMobileApi.Infrastructure.Data.Repositories.Alerts;
using Microsoft.Extensions.Logging;

namespace iRISMobileApi.Core.Alerts
{
    public class AlertsService : IAlertsService
    {
        private readonly ILogger<AlertsService> _logger;
        private readonly IAlertsRepository _alertsRepository;

        public AlertsService(ILogger<AlertsService> logger, IAlertsRepository alertsRepository)
        {
            _logger = logger;
            _alertsRepository = alertsRepository;
        }

        public async Task<List<object>> GetAlerts(string? appType, Dictionary<string, string> parameters)
        {
            try
            {
                _logger.LogInformation("GetProductsTemplate of AlertsService is invoked");
                _logger.LogInformation("GetProductsTemplate with parameters: {0} are being fetched", parameters);
                var alertsList = await _alertsRepository.GetAlerts(appType, parameters);
                if (alertsList != null && alertsList.Count > 0)
                {
                    _logger.LogDebug("alertsList of {0} records received for parameters: '{1}'", alertsList.Count, parameters);
                    return alertsList;
                }
                _logger.LogInformation("Service response for GetAlerts does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetAlerts in AlertsService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetAlerts in AlertsService: {ex.ToJson()}");
                throw;
            }
        }

        public async Task<object> GetAlertDetails(string? appType, Dictionary<string, string> detailsParameters)
        {
            try
            {
                _logger.LogInformation("GetAlertDetails of AlertsService is invoked");
                var alertDetails = await _alertsRepository.GetAlertDetails(appType, detailsParameters);
                if (alertDetails != null)
                    return alertDetails;
                _logger.LogInformation("Service response for GetAlertDetails does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetAlertDetails in AlertsService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetAlertDetails in AlertsService: {ex.ToJson()}");
                throw;
            }
        }
    }
}