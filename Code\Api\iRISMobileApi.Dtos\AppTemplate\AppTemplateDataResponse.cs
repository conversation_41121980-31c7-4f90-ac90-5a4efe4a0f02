﻿namespace iRISMobileApi.Dtos.AppTemplate
{
    public class AppTemplateDataResponse
    {
        public string? AppScreenCode { get; set; }

        public string? AppScreenName { get; set; }

        public long? ParentAppScreenID { get; set; }

        public string? ParentAppScreenCode { get; set; }

        public string? ParentAppScreenName { get; set; }

        public AppScreenContent? AppScreenContent { get; set; }
    }

    public class AppScreenContent
    {
        public List<AppScreenSections>? AppScreenSections { get; set; }
    }

    public class AppScreenSections
    {
        public string? AppScreenSectionCode { get; set; }

        public string? AppScreenSectionName { get; set; }

        public string? AppScreenSectionTypeCode { get; set; }

        public string? AppScreenSectionType { get; set; }

        public int? AppScreenSectionOrder { get; set; }

        public bool? AppScreenSectionEnabled { get; set; }

        public bool? AppScreenSectionDetailIconEnabled { get; set; }

        public bool? AppScreenSectionSearchBarEnabled { get; set; }

        public bool? AppScreenSectionExpanderEnabled { get; set; }

        public List<AppScreenSectionServices>? AppScreenSectionServices { get; set; }

        public List<AppScreenSectionContent>? AppScreenSectionContent { get; set; }
    }

    public class AppScreenSectionServices
    {
        public string? ServiceCategory { get; set; }
        public string? ServiceName { get; set; }
    }

    public class AppScreenSectionContent
    {
        public string? AppFieldCode { get; set; }

        public string? AppFieldName { get; set; }

        public string? AppFieldColumnName { get; set; }

        public string? AppFieldMetaDataKey { get; set; }

        public string? AppScreenSectionFieldLabel { get; set; }

        public bool? IsFieldLabelEnabled { get; set; }

        public bool? IsFieldValueEnabled { get; set; }

        public FontSettings? FieldLabelFontSettings { get; set; }

        public FontSettings? FieldValueFontSettings { get; set; }

        public FieldSizes? FieldLabelSizeSettings { get; set; }

        public FieldSizes? FieldValueSizeSettings { get; set; }

        public bool? IsIconEnabled { get; set; }

        public int? AppScreenSectionFieldRowOrder { get; set; }

        public int? AppScreenSectionFieldColumnOrder { get; set; }

        public bool? IsFieldValueEditable { get; set; }

        public bool? IsFieldValueValidate { get; set; }

        public string? FieldValueTypeCode { get; set; }

        public string? FieldValueType { get; set; }

        public string? FieldValueDataTypeCode { get; set; }

        public string? FieldValueDataType { get; set; }

        public bool? IsDisplayInExpander { get; set; }
    }

    public class FontSettings
    {
        public string? FontSettingCode { get; set; }

        public string? FontFamily { get; set; }

        public string? FontSize { get; set; }

        public string? FontColor { get; set; }

        public string? FontWeight { get; set; }
    }

    public class FieldSizes
    {
        public decimal? FieldWidth { get; set; }

        public decimal? FieldHeight { get; set; }
    }
}
