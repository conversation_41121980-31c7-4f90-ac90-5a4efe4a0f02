﻿using iRISMobileApi.Core.Products;
using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.AppTemplate;
using iRISMobileApi.Dtos.Products;
using iRISMobileApi.Infrastructure.Data.Repositories.AppTemplate;
using iRISMobileApi.Infrastructure.Data.Repositories.Products;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iRISMobileApi.Core.AppTemplate
{
    public class AppTemplateDataService : IAppTemplateDataService
    {
        private readonly ILogger<AppTemplateDataService> _logger;
        private readonly IAppTemplateDataRepository _appTemplateDataRepository;

        public AppTemplateDataService(ILogger<AppTemplateDataService> logger, IAppTemplateDataRepository appTemplateDataRepository)
        {
            _logger = logger;
            _appTemplateDataRepository = appTemplateDataRepository;
        }


        public async Task<List<AppTemplateDataResponse>> GetAppTemplateData(string? appScreenName)
        {
            List<AppTemplateDataResponse> lstAppTemplateDataResponse = new List<AppTemplateDataResponse>();
            List<AppScreenSectionServicesDataResult> lstAppScreenSectionServicesData = new List<AppScreenSectionServicesDataResult>();
            try
            {
                _logger.LogInformation("GetAppTemplateData of AppTemplateDataService is invoked");
                var appTemplateDataList = await _appTemplateDataRepository.GetAppTemplateData(appScreenName);
                if (appTemplateDataList != null && appTemplateDataList.Count > 0)
                {
                    _logger.LogDebug("appTemplateDataList of {0} records received", appTemplateDataList.Count);
                    var appScreenSectionServicesDataList = await _appTemplateDataRepository.GetAppScreenSectionServicesData();
                    if(appScreenSectionServicesDataList != null)
                    {
                        lstAppScreenSectionServicesData = appScreenSectionServicesDataList;
                    }
                    var qryAppScreensData = (from u in appTemplateDataList
                                             where u.AppScreenCode != null && u.AppScreenName != null
                                             select new
                                             {
                                                 u.AppScreenID,
                                                 u.AppScreenCode,
                                                 u.AppScreenName,
                                                 u.ParentAppScreenID,
                                                 u.ParentAppScreenCode,
                                                 u.ParentAppScreenName
                                             })
                                            .Distinct()
                                            .OrderBy(p => p.AppScreenID);

                    if(qryAppScreensData != null)
                    {
                        foreach(var eachAppScreenDataItem in qryAppScreensData)
                        {
                            AppTemplateDataResponse appTemplateDataResponse = new AppTemplateDataResponse();
                            long? appScreenID = eachAppScreenDataItem.AppScreenID;                            
                            appTemplateDataResponse.AppScreenCode = eachAppScreenDataItem.AppScreenCode;
                            appTemplateDataResponse.AppScreenName = eachAppScreenDataItem.AppScreenName;
                            appTemplateDataResponse.ParentAppScreenID = eachAppScreenDataItem.ParentAppScreenID;
                            appTemplateDataResponse.ParentAppScreenCode = eachAppScreenDataItem.ParentAppScreenCode;
                            appTemplateDataResponse.ParentAppScreenName = eachAppScreenDataItem.ParentAppScreenName;
                            AppScreenContent asc = new AppScreenContent();
                            List<AppScreenSections> lstAppScreenSections = new List<AppScreenSections>();
                            if (appScreenID != null)
                            {
                                var qryAppScreenSectionsData = (from u in appTemplateDataList
                                                                where u.AppScreenID != null && u.AppScreenID == appScreenID
                                                                      && u.AppScreenSectionCode != null && u.AppScreenSectionName != null
                                                                select new
                                                                {
                                                                    u.AppScreenSectionID,
                                                                    u.AppScreenSectionCode,
                                                                    u.AppScreenSectionName,
                                                                    u.AppScreenSectionTypeCode,
                                                                    u.AppScreenSectionType,
                                                                    u.AppScreenSectionOrder,
                                                                    u.AppScreenSectionEnabled,
                                                                    u.AppScreenSectionDetailIconEnabled,
                                                                    u.AppScreenSectionSearchBarEnabled,
                                                                    u.AppScreenSectionExpanderEnabled
                                                                })
                                                                .Distinct()
                                                                .OrderBy(p => p.AppScreenSectionID);
                                if (qryAppScreenSectionsData != null)
                                {
                                    List<AppScreenSectionServicesDataResult> lstAppScreenSectionServicesFilteredData = new List<AppScreenSectionServicesDataResult>();
                                    List<AppScreenSectionServices> lstAppScreenSectionServices = new List<AppScreenSectionServices>();
                                    foreach (var eachAppScreenSectionItem in qryAppScreenSectionsData)
                                    {
                                        AppScreenSections apss = new AppScreenSections();
                                        int appScreenSectionID = eachAppScreenSectionItem.AppScreenSectionID;
                                        apss.AppScreenSectionCode = eachAppScreenSectionItem.AppScreenSectionCode;
                                        apss.AppScreenSectionName = eachAppScreenSectionItem.AppScreenSectionName;
                                        apss.AppScreenSectionTypeCode = eachAppScreenSectionItem.AppScreenSectionTypeCode;
                                        apss.AppScreenSectionType = eachAppScreenSectionItem.AppScreenSectionType;
                                        apss.AppScreenSectionOrder = eachAppScreenSectionItem.AppScreenSectionOrder;
                                        apss.AppScreenSectionEnabled = eachAppScreenSectionItem.AppScreenSectionEnabled;
                                        apss.AppScreenSectionDetailIconEnabled = eachAppScreenSectionItem.AppScreenSectionDetailIconEnabled;
                                        apss.AppScreenSectionSearchBarEnabled = eachAppScreenSectionItem.AppScreenSectionSearchBarEnabled;
                                        apss.AppScreenSectionExpanderEnabled = eachAppScreenSectionItem.AppScreenSectionExpanderEnabled;
                                        if (lstAppScreenSectionServicesData != null && lstAppScreenSectionServicesData.Count() > 0)
                                        {
                                            
                                            var qryAppScreenSectionServicesFilteredData = (from u in lstAppScreenSectionServicesData
                                                                                           where u.AppScreenSectionID == appScreenSectionID
                                                                                           select u);
                                            if(qryAppScreenSectionServicesFilteredData != null)
                                            {
                                                lstAppScreenSectionServicesFilteredData = qryAppScreenSectionServicesFilteredData.ToList();
                                            }
                                            if(lstAppScreenSectionServicesFilteredData != null && lstAppScreenSectionServicesFilteredData.Count() > 0)
                                            {
                                                foreach(var eachItem in lstAppScreenSectionServicesFilteredData)
                                                {
                                                    AppScreenSectionServices asss = new AppScreenSectionServices();
                                                    asss.ServiceCategory = eachItem.AppServiceCategory;
                                                    asss.ServiceName = eachItem.AppServiceName;
                                                    lstAppScreenSectionServices.Add(asss);
                                                }
                                            }
                                        }
                                        apss.AppScreenSectionServices = lstAppScreenSectionServices;
                                        List<AppScreenSectionContent> lstAppScreenSectionContent = new List<AppScreenSectionContent>();
                                        var qryAppScreenSectionContentData = (from u in appTemplateDataList
                                                                              where u.AppScreenID != null && u.AppScreenID == appScreenID
                                                                                   && u.AppScreenSectionID == appScreenSectionID
                                                                              select new
                                                                              {
                                                                                  u.AppFieldID,
                                                                                  u.AppFieldCode,
                                                                                  u.AppFieldName,
                                                                                  u.AppFieldColumnName,
                                                                                  u.AppFieldMetaDataKey,
                                                                                  u.AppScreenSectionFieldLabel,
                                                                                  u.IsFieldLabelEnabled,
                                                                                  u.IsFieldValueEnabled,
                                                                                  u.FieldLabelFontSettingCode,
                                                                                  u.FieldLabelFontFamily,
                                                                                  u.FieldLabelFontSize,
                                                                                  u.FieldLabelFontColor,
                                                                                  u.FieldLabelFontWeight,
                                                                                  u.FieldValueFontSettingCode,
                                                                                  u.FieldValueFontFamily,
                                                                                  u.FieldValueFontSize,
                                                                                  u.FieldValueFontColor,
                                                                                  u.FieldValueFontWeight,
                                                                                  u.IsIconEnabled,
                                                                                  u.AppScreenSectionFieldRowOrder,
                                                                                  u.AppScreenSectionFieldColumnOrder,
                                                                                  u.FieldLabelWidth,
                                                                                  u.FieldLabelHeight,
                                                                                  u.FieldValueWidth,
                                                                                  u.FieldValueHeight,
                                                                                  u.IsFieldValueEditable,
                                                                                  u.IsFieldValueValidate,
                                                                                  u.FieldValueTypeCode,
                                                                                  u.FieldValueType,
                                                                                  u.FieldValueDataTypeCode,
                                                                                  u.FieldValueDataType,
                                                                                  u.IsDisplayInExpander
                                                                              })
                                                                              .OrderBy(p => p.AppFieldID);

                                        if (qryAppScreenSectionContentData != null)
                                        {
                                            foreach (var eachAppScreenSectionContentItem in qryAppScreenSectionContentData)
                                            {
                                                AppScreenSectionContent apssc = new AppScreenSectionContent();
                                                apssc.AppFieldCode = eachAppScreenSectionContentItem.AppFieldCode;
                                                apssc.AppFieldName = eachAppScreenSectionContentItem.AppFieldName;
                                                apssc.AppFieldColumnName = eachAppScreenSectionContentItem.AppFieldColumnName;
                                                apssc.AppFieldMetaDataKey = eachAppScreenSectionContentItem.AppFieldMetaDataKey;
                                                apssc.AppScreenSectionFieldLabel = eachAppScreenSectionContentItem.AppScreenSectionFieldLabel;
                                                apssc.IsFieldLabelEnabled = eachAppScreenSectionContentItem.IsFieldLabelEnabled;
                                                apssc.IsFieldValueEnabled = eachAppScreenSectionContentItem.IsFieldValueEnabled;
                                                FontSettings fieldLabelFontSettings = new FontSettings();
                                                fieldLabelFontSettings.FontSettingCode = eachAppScreenSectionContentItem.FieldLabelFontSettingCode;
                                                fieldLabelFontSettings.FontFamily = eachAppScreenSectionContentItem.FieldLabelFontFamily;
                                                fieldLabelFontSettings.FontSize = eachAppScreenSectionContentItem.FieldLabelFontSize;
                                                fieldLabelFontSettings.FontColor = eachAppScreenSectionContentItem.FieldLabelFontColor;
                                                fieldLabelFontSettings.FontWeight = eachAppScreenSectionContentItem.FieldLabelFontWeight;
                                                apssc.FieldLabelFontSettings = fieldLabelFontSettings;
                                                FontSettings fieldValueFontSettings = new FontSettings();
                                                fieldValueFontSettings.FontSettingCode = eachAppScreenSectionContentItem.FieldValueFontSettingCode;
                                                fieldValueFontSettings.FontFamily = eachAppScreenSectionContentItem.FieldValueFontFamily;
                                                fieldValueFontSettings.FontSize = eachAppScreenSectionContentItem.FieldValueFontSize;
                                                fieldValueFontSettings.FontColor = eachAppScreenSectionContentItem.FieldValueFontColor;
                                                fieldValueFontSettings.FontWeight = eachAppScreenSectionContentItem.FieldValueFontWeight;
                                                apssc.FieldValueFontSettings = fieldValueFontSettings;
                                                FieldSizes fieldLabelSizeSettings = new FieldSizes();
                                                fieldLabelSizeSettings.FieldWidth = eachAppScreenSectionContentItem.FieldLabelWidth;
                                                fieldLabelSizeSettings.FieldHeight = eachAppScreenSectionContentItem.FieldLabelHeight;
                                                apssc.FieldLabelSizeSettings = fieldLabelSizeSettings;
                                                FieldSizes fieldValueSizeSettings = new FieldSizes();
                                                fieldValueSizeSettings.FieldWidth = eachAppScreenSectionContentItem.FieldValueWidth;
                                                fieldValueSizeSettings.FieldHeight = eachAppScreenSectionContentItem.FieldValueHeight;
                                                apssc.FieldValueSizeSettings = fieldValueSizeSettings;
                                                apssc.IsIconEnabled = eachAppScreenSectionContentItem.IsIconEnabled;
                                                apssc.AppScreenSectionFieldRowOrder = eachAppScreenSectionContentItem.AppScreenSectionFieldRowOrder;
                                                apssc.AppScreenSectionFieldColumnOrder = eachAppScreenSectionContentItem.AppScreenSectionFieldColumnOrder;
                                                apssc.IsFieldValueEditable = eachAppScreenSectionContentItem.IsFieldValueEditable;
                                                apssc.IsFieldValueValidate = eachAppScreenSectionContentItem.IsFieldValueValidate;
                                                apssc.FieldValueTypeCode = eachAppScreenSectionContentItem.FieldValueTypeCode;
                                                apssc.FieldValueType = eachAppScreenSectionContentItem.FieldValueType;
                                                apssc.FieldValueDataTypeCode = eachAppScreenSectionContentItem.FieldValueDataTypeCode;
                                                apssc.FieldValueDataType = eachAppScreenSectionContentItem.FieldValueDataType;
                                                apssc.IsDisplayInExpander = eachAppScreenSectionContentItem.IsDisplayInExpander;
                                                lstAppScreenSectionContent.Add(apssc);
                                            }
                                        }
                                        apss.AppScreenSectionContent = lstAppScreenSectionContent;
                                        lstAppScreenSections.Add(apss);
                                    }
                                }
                            }
                            asc.AppScreenSections = lstAppScreenSections;
                            appTemplateDataResponse.AppScreenContent = asc;
                            lstAppTemplateDataResponse.Add(appTemplateDataResponse);
                        }                         
                    }             
                    return lstAppTemplateDataResponse;
                }
                _logger.LogInformation("Service response for GetAppTemplateData does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetAppTemplateData in AppTemplateDataService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetAppTemplateData in AppTemplateDataService: {ex.ToJson()}");
                throw;
            }
        }

        public async Task<List<AppScreenSectionServicesDataResult>> GetAppScreenSectionServicesData()
        {
            try
            {
                _logger.LogInformation("GetAppScreenSectionServicesData of AppTemplateDataService is invoked");
                var appScreenSectionServicesDataList = await _appTemplateDataRepository.GetAppScreenSectionServicesData();
                if (appScreenSectionServicesDataList != null && appScreenSectionServicesDataList.Count > 0)
                {
                    _logger.LogDebug("appScreenSectionServicesDataList of {0} records received", appScreenSectionServicesDataList.Count);
                    return appScreenSectionServicesDataList;
                }
                _logger.LogInformation("Service response for GetAppScreenSectionServicesData does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetAppScreenSectionServicesData in AppTemplateDataService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetAppScreenSectionServicesData in AppTemplateDataService: {ex.ToJson()}");
                throw;
            }
        }
    }
}
