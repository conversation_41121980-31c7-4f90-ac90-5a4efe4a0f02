﻿using System.Net;
using iRISMobileApi.Core.Alerts;
using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.Model.Configs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

namespace iRISMobileApi.Controllers
{
    [ApiController]
    [Produces("application/json")]
    public class AlertsController : ControllerBase
    {
        private readonly ILogger<AlertsController> _logger;
        private readonly IAlertsService _alertsService;
        private readonly IMemoryCache _cache;
        IOptions<CacheOptions> _cacheOptions;

        public AlertsController(ILogger<AlertsController> logger, IAlertsService alertsService, IMemoryCache cache, IOptions<CacheOptions> cacheOptions)
        {
            _logger = logger;
            _alertsService = alertsService;
            _cache = cache;
            _cacheOptions = cacheOptions;
        }

        [Route("alerts"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<List<object>>>> FetchAlerts([FromBody] ListRequest listRequest)
        {
            ApiResponse<List<object>> result;
            try
            {
                _logger.LogInformation("alerts route is invoked");
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<List<object>>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for FetchAlerts",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("FetchAlerts route generated failure response {0}", result.ToJson());
                    return result;
                }
                string cacheKey = "alerts";

                List<object> response;
                if (!listRequest.requestingApp.forceRefresh && _cache.TryGetValue(cacheKey, out List<object> data))
                {
                    response = data;
                }
                else
                {
                    response = await _alertsService.GetAlerts(listRequest.requestingApp.appType, listRequest.listInfo.parameters);
                    if (response == null || response?.Count < 1)
                    {
                        result = new ApiResponse<List<object>>
                        {
                            status = ApiResponseStatus.FAIL,
                            code = HttpStatusCode.NoContent,
                            error = new Error
                            {
                                message = "no alerts found for given search characters"
                            }
                        };
                        _logger.LogInformation("alerts route generated failure response {0}", result.ToJson());
                        return result;
                    }
                    var cacheOptions = new MemoryCacheEntryOptions();
                    cacheOptions.SetAbsoluteExpiration(TimeSpan.FromMinutes(_cacheOptions.Value.ExpirationInMinutes));

                    _cache.Set(cacheKey, response, cacheOptions); result = new ApiResponse<List<object>>
                    {
                        status = ApiResponseStatus.SUCCESS,
                        code = HttpStatusCode.OK,
                        data = response.Take(20).ToList()
                    };
                }
                result = new ApiResponse<List<object>>
                {
                    status = ApiResponseStatus.SUCCESS,
                    code = HttpStatusCode.OK,
                    data = response.Take(20).ToList()
                };
                _logger.LogInformation("alerts route generated success response {0}", result.ToJson());
            }
            catch (Exception ex)
            {
                result = new ApiResponse<List<object>>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to fetch alerts list due to internal server error."
                    }
                };
                _logger.LogError("An internal server error occured, {0}", ex.Message);
                _logger.LogError("alerts route generated error response {0}", result.ToJson());
            }
            return result;
        }

        [Route("alert-details"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<object>>> FetchAlertDetails([FromBody]DetailsRequest detailsRequest)
        {
            ApiResponse<object> result;
            try
            {
                _logger.LogInformation("alert-details route is invoked");
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<object>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for FetchAlertDetails",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("FetchAlertDetails route generated failure response {0}", result.ToJson());
                    return result;
                }
                string cacheKey = "alert-details";

                object response;
                if (!detailsRequest.requestingApp.forceRefresh && _cache.TryGetValue(cacheKey, out object data))
                {
                    response = data;
                }
                else
                {
                    response = await _alertsService.GetAlertDetails(detailsRequest.requestingApp.appType, detailsRequest.detailsInfo.detailsParameters);
                    if (response == null)
                    {
                        result = new ApiResponse<object>
                        {
                            status = ApiResponseStatus.FAIL,
                            code = HttpStatusCode.NoContent,
                            error = new Error
                            {
                                message = "no alert found for given search parameters"
                            }
                        };
                        _logger.LogInformation("alert-details route generated failure response {0}", result.ToJson());
                        return result;
                    }
                    var cacheOptions = new MemoryCacheEntryOptions();
                    cacheOptions.SetAbsoluteExpiration(TimeSpan.FromMinutes(_cacheOptions.Value.ExpirationInMinutes));

                    _cache.Set(cacheKey, response, cacheOptions); result = new ApiResponse<object>
                    {
                        status = ApiResponseStatus.SUCCESS,
                        code = HttpStatusCode.OK,
                        data = response
                    };
                }
                result = new ApiResponse<object>
                {
                    status = ApiResponseStatus.SUCCESS,
                    code = HttpStatusCode.OK,
                    data = response
                };
                _logger.LogInformation("alert-details route generated success response {0}", result.ToJson());
            }
            catch (Exception ex)
            {
                result = new ApiResponse<object>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to fetch alert details due to internal server error."
                    }
                };
                _logger.LogError("alert-details route generated error response {0}", result.ToJson());
            }
            return result;
        }
    }
}
