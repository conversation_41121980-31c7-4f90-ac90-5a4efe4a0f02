﻿using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.Products;
using iRISMobileApi.Infrastructure.Data.Repositories.Products;
using Microsoft.Extensions.Logging;

namespace iRISMobileApi.Core.Products
{
    public class ProductsService : IProductsService
    {
        private readonly ILogger<ProductsService> _logger;
        private readonly IProductsRepository _productsRepository;

        public ProductsService(ILogger<ProductsService> logger, IProductsRepository productsRepository)
        {
            _logger = logger;
            _productsRepository = productsRepository;
        }
        public async Task<ProductsResponse> GetProducts(string? appType, string searchString)
        {
            ProductsResponse productsResponse = new ProductsResponse();
            try
            {
                _logger.LogInformation("GetProducts of ProductsService is invoked");
                _logger.LogInformation("Products with searchString: {0} are being fetched", searchString);
                var productsList = await _productsRepository.GetProducts(appType, searchString);
                if (productsList != null && productsList.Count > 0) 
                {
                    _logger.LogDebug("productsList of {0} records received for searchString: '{1}'", productsList.Count, searchString);
                    productsResponse.productsList = productsList;
                    return productsResponse;
                }
                _logger.LogInformation("Service response for GetProducts does not generate any result");
                return null;                
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetProducts in ProductsService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetProducts in ProductsService: {ex.ToJson()}");
                throw;
            }
        }

        public async Task<List<object>> GetProductsTemplate(string? appType, Dictionary<string, string> parameters)
        {
            try
            {
                _logger.LogInformation("GetProductsTemplate of ProductsService is invoked");
                _logger.LogInformation("GetProductsTemplate with parameters: {0} are being fetched", parameters);
                var productsList = await _productsRepository.GetProductsTemplate(appType, parameters);
                if (productsList != null && productsList.Count > 0)
                {
                    _logger.LogDebug("productsList of {0} records received for parameters: '{1}'", productsList.Count, parameters);
                    return productsList;
                }
                _logger.LogInformation("Service response for GetProductsTemplate does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetProductsTemplate in ProductsService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetProductsTemplate in ProductsService: {ex.ToJson()}");
                throw;
            }
        }

        public async Task<ProductDetailsResponse> GetProductDetails(string? appType, int productId)
        {
            ProductDetailsResponse productDetailsResponse = new ProductDetailsResponse();
            try
            {
                List<ProductDetails> lstProductDetails = new List<ProductDetails>();
                _logger.LogInformation("GetProducts of ProductsService is invoked");
                _logger.LogInformation("Products with productId: {0} are being fetched", productId);
                var productLocationSummary = await _productsRepository.GetProductLocationSummary(appType, productId);
                var productLocationDetails = await _productsRepository.GetProductLocationDetails(appType, productId);
                if(productLocationSummary != null && productLocationSummary.Count() > 0)
                {
                    foreach(var eachProductLocationSummaryItem in productLocationSummary)
                    {
                        ProductDetails pd = new ProductDetails();
                        var location = eachProductLocationSummaryItem.Location;
                        pd.Location = location;
                        pd.TotalQty = eachProductLocationSummaryItem.TotalQty;
                        //pd.Department = eachProductLocationSummaryItem.Department;
                        pd.Label_Location = eachProductLocationSummaryItem.Label_Location;
                        pd.Label_Qty = eachProductLocationSummaryItem.Label_Qty;
                        List<ProductLocationDetails> lstProductLocationDetails = new List<ProductLocationDetails>();
                        if(productLocationDetails != null && productLocationDetails.Count() > 0)
                        {
                            var filteredProductLocationDetails = productLocationDetails.Where(p => p.Location != null && p.Location.Equals(location)).ToList();
                            if(filteredProductLocationDetails != null && filteredProductLocationDetails.Count() > 0)
                            {
                                pd.productLocationDetails = filteredProductLocationDetails;
                            }                            
                        }
                        lstProductDetails.Add(pd);
                    }
                    productDetailsResponse.productDetailsList = lstProductDetails;
                    return productDetailsResponse;
                }
                
                /*
                var productDetails = await _productsRepository.GetProductDetails(appType, productId);
                if (productDetails != null && productDetails.Count > 0)
                {
                    _logger.LogDebug("Successfully receved product details for productId: '{0}'", productId);
                    productDetailsResponse.productDetailsList = productDetails;
                    return productDetailsResponse;
                }
                */
                _logger.LogInformation("Service response for GetProductDetails does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetProductDetails in ProductsService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetProductDetails in ProductsService: {ex.ToJson()}");
                throw;
            }
        }

        public async Task<object> GetProductDetailsTemplate(string? appType, Dictionary<string, string> detailsParameters)
        {
            List<object> productDetailsResponse = new List<object>();
            try
            {
                List<ProductDetails> lstProductDetails = new List<ProductDetails>();
                _logger.LogInformation("GetProducts of ProductsService is invoked");
                _logger.LogInformation("Products with productId: {0} are being fetched", detailsParameters);
                var productLocationSummary = await _productsRepository.GetProductLocationSummaryTemplate(appType, detailsParameters);
                var productLocationDetails = await _productsRepository.GetProductLocationDetailsTemplate(appType, detailsParameters);
                if (productLocationSummary != null && productLocationSummary.Count() > 0)
                {
                    foreach (var plsObj in productLocationSummary)
                    {
                        ProductDetails pd = new ProductDetails
                        {
                            Location = plsObj.Location,
                            TotalQty = plsObj.TotalQty
                        };
                        List<ProductLocationDetails> lstProductLocationDetails = new List<ProductLocationDetails>();
                        if (productLocationDetails != null && productLocationDetails.Count() > 0)
                        {
                            var filteredProductLocationDetails = productLocationDetails.Where(p => p.Location != null && p.Location.Equals(plsObj.Location)).ToList();
                            if (filteredProductLocationDetails != null && filteredProductLocationDetails.Count() > 0)
                            {
                                pd.productLocationDetails = filteredProductLocationDetails;
                            }
                        }
                        productDetailsResponse.Add(pd);
                    }
                    return productDetailsResponse;
                }
                _logger.LogInformation("Service response for GetProductDetails does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetProductDetails in ProductsService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetProductDetails in ProductsService: {ex.ToJson()}");
                throw;
            }
        }
    }
}
