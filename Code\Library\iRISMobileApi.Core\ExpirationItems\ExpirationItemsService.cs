﻿using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.ExpirationItems;
using iRISMobileApi.Infrastructure.Data.Repositories.ExpiredItem;
using Microsoft.Extensions.Logging;

namespace iRISMobileApi.Core.ExpiredItem
{
    public class ExpirationItemsService : IExpirationItemsService
    {
        private readonly ILogger<IExpirationItemsService> _logger;
        private readonly IExpirationItemsRepository _expirationItemsRepository;

        public ExpirationItemsService(ILogger<IExpirationItemsService> logger, IExpirationItemsRepository expiredItemsRepository)
        {
            _logger = logger;
            _expirationItemsRepository = expiredItemsRepository;
        }
        public async Task<ExpirationItemsResponse> GetExpirationItems(string? appType, string searchString)
        {
            ExpirationItemsResponse expirationItemsResponse = new();
            try
            {
                _logger.LogInformation("GetExpirationItems of ExpirationItemsService is invoked");
                _logger.LogInformation("Expiration items with searchString: {0} are being fetched", searchString);
                var expirationItemsList = await _expirationItemsRepository.GetExpirationItems(appType, searchString);
                if (expirationItemsList != null && expirationItemsList.Count > 0)
                {
                    _logger.LogDebug("expirationItemsList of {0} records received for searchString: '{1}'", expirationItemsList.Count, searchString);
                    expirationItemsResponse.expirationItemsList = expirationItemsList;
                    return expirationItemsResponse;
                }
                _logger.LogInformation("Service response for GetExpirationItems does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetExpirationItems in ExpirationItemsService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetExpirationItems in ExpirationItemsService: {ex.ToJson()}");
                throw;
            }
        }

        public async Task<List<object>> GetExpirationItemsTemplate(string? appType, Dictionary<string, string> parameters)
        {
            try
            {
                _logger.LogInformation("GetExpirationItemsTemplate of ExpirationItemsService is invoked");
                _logger.LogInformation("Expiration items with given parameters: {0} are being fetched", parameters);
                var expirationItemsList = await _expirationItemsRepository.GetExpirationItemsTemplate(appType, parameters);
                if (expirationItemsList != null && expirationItemsList.Count > 0)
                {
                    _logger.LogDebug("expirationItemsList of {0} records received for given parameters: '{1}'", expirationItemsList.Count, parameters);
                    return expirationItemsList;
                }
                _logger.LogInformation("Service response for GetExpirationItems does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetExpirationItems in ExpirationItemsService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetExpirationItems in ExpirationItemsService: {ex.ToJson()}");
                throw;
            }
        }

        public async Task<List<ExpirationItemDetailsResponse>> GetExpirationItemDetails(string? appType, string catalogNo)
        {
            List<ExpirationItemDetailsResponse> expirationItemDetailsListResponse;
            try
            {
                _logger.LogInformation("GetExpirationItemsDetails of ExpirationItemsService is invoked");
                _logger.LogInformation("Expiration items with catalogNo: {0} are being fetched", catalogNo);
                var expirationItemsDetails = await _expirationItemsRepository.GetExpirationItemDetails(appType, catalogNo);
                if (expirationItemsDetails != null && expirationItemsDetails.Count > 0)
                {
                    _logger.LogDebug("Successfully receved expiration item details for searchString: '{0}'", catalogNo);
                    var allDetailsList = new List<ExpirationItemDetailsModel>();
                    foreach (var obj in expirationItemsDetails)
                    {
                        allDetailsList.Add(new ExpirationItemDetailsModel
                        {
                            CatalogNo = obj.CatalogNo,
                            ExpiredDate = string.IsNullOrEmpty(obj.ExpiredDate) ? null : Convert.ToDateTime(obj.ExpiredDate),
                            RFID = obj.RFID,
                            ClusterName = obj.ClusterName,
                            CabinetName = obj.CabinetName,
                            CabinetType = obj.CabinetType,
                            ClusterLocation = obj.ClusterLocation,
                            Location = obj.Location,
                            SerialNo = obj.SerialNo,
                            ItemStatus = obj.Status,
                            ScanDate = string.IsNullOrEmpty(obj.ScanDate) ? null : Convert.ToDateTime(obj.ScanDate),
                            ScanUser = obj.ScanUser,
                            Label_Expiration = obj.Label_Expiration,
                            Label_RFID = obj.Label_RFID,
                            Label_ScanDate = obj.Label_ScanDate,
                            Label_ScanUser = obj.Label_ScanUser,
                            Label_Status = obj.Label_Status,
                            Label_SerialNo = obj.Label_SerialNo
                        });
                    }

                    var groupedData = allDetailsList.OrderBy(x => x.Location).GroupBy(f => f.Location).Select(f => new ExpirationItemDetailsResponse(f.Key.ToString(), f.ToList().Select((item, index) =>
                    {
                        item.Index = index + 1;
                        return item;
                    }).ToList(), f.ToList().Count()));
                    expirationItemDetailsListResponse = groupedData.ToList();
                    return expirationItemDetailsListResponse;
                }
                _logger.LogInformation("Service response for GetExpirationItemsDetails does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetExpirationItemDetails in ExpirationItemsService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetExpirationItemDetails in ExpirationItemsService: {ex.ToJson()}");
                throw;
            }
        }

        public async Task<object> GetExpirationItemDetailsTemplate(string? appType, Dictionary<string, string> detailsParameters)
        {
            try
            {
                List<dynamic> detailsResponse = new();
                _logger.LogInformation("GetExpirationItemDetailsTemplate of ExpirationItemsService is invoked");
                var expirationItemsDetails = await _expirationItemsRepository.GetExpirationItemDetailsTemplate(appType, detailsParameters);
                if (expirationItemsDetails != null && expirationItemsDetails.Count > 0)
                {
                    foreach (var edLstObj in expirationItemsDetails)
                    {
                        if (detailsResponse.Any(x => x.Location == edLstObj.Location))
                            continue;
                        var eidObj = new ExpirationItemDetailsTemplate
                        {
                            Location = edLstObj.Location
                        };                        
                        var filteredEdLst = expirationItemsDetails.Where(p => p.Location != null && p.Location.Equals(edLstObj.Location)).ToList();
                        if (filteredEdLst != null && filteredEdLst.Count() > 0)
                        {
                            eidObj.TotalQty = filteredEdLst.Count;
                            List<ExpirationItemLocationDetails> expirationItemLocationDetails = new List<ExpirationItemLocationDetails>();
                            foreach (var filteredEdLstObj in filteredEdLst)
                            {
                                expirationItemLocationDetails.Add(
                                    new ExpirationItemLocationDetails
                                    {
                                        ExpiredDate = string.IsNullOrEmpty(filteredEdLstObj.ExpiredDate) ? null : Convert.ToDateTime(filteredEdLstObj.ExpiredDate),
                                        RFID = filteredEdLstObj.RFID,
                                        SerialNo = filteredEdLstObj.SerialNo,
                                        ItemStatus = filteredEdLstObj.Status
                                    }
                                );
                            }
                            eidObj.ExpirationItemLocationDetails = expirationItemLocationDetails;
                        }
                        detailsResponse.Add(eidObj);
                    }
                    return detailsResponse;
                }
                _logger.LogInformation("Service response for GetExpirationItemsDetails does not generate any result");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while calling GetExpirationItemDetails in ExpirationItemsService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling GetExpirationItemDetails in ExpirationItemsService: {ex.ToJson()}");
                throw;
            }
        }
    }
}
