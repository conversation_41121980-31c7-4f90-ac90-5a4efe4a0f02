{"version": 3, "targets": {"net6.0": {"AzureSignTool/4.0.1": {"type": "package"}}, "net6.0/any": {"AzureSignTool/4.0.1": {"type": "package", "tools": {"tools/net6.0/any/Azure.Core.dll": {}, "tools/net6.0/any/Azure.Identity.dll": {}, "tools/net6.0/any/Azure.Security.KeyVault.Certificates.dll": {}, "tools/net6.0/any/Azure.Security.KeyVault.Keys.dll": {}, "tools/net6.0/any/AzureSign.Core.dll": {}, "tools/net6.0/any/AzureSign.Core.pdb": {}, "tools/net6.0/any/AzureSign.Core.xml": {}, "tools/net6.0/any/AzureSignTool.deps.json": {}, "tools/net6.0/any/AzureSignTool.dll": {}, "tools/net6.0/any/AzureSignTool.pdb": {}, "tools/net6.0/any/AzureSignTool.runtimeconfig.json": {}, "tools/net6.0/any/DotnetToolSettings.xml": {}, "tools/net6.0/any/McMaster.Extensions.CommandLineUtils.dll": {}, "tools/net6.0/any/Microsoft.Bcl.AsyncInterfaces.dll": {}, "tools/net6.0/any/Microsoft.Extensions.Configuration.Abstractions.dll": {}, "tools/net6.0/any/Microsoft.Extensions.Configuration.Binder.dll": {}, "tools/net6.0/any/Microsoft.Extensions.Configuration.dll": {}, "tools/net6.0/any/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}, "tools/net6.0/any/Microsoft.Extensions.DependencyInjection.dll": {}, "tools/net6.0/any/Microsoft.Extensions.Logging.Abstractions.dll": {}, "tools/net6.0/any/Microsoft.Extensions.Logging.Configuration.dll": {}, "tools/net6.0/any/Microsoft.Extensions.Logging.Console.dll": {}, "tools/net6.0/any/Microsoft.Extensions.Logging.dll": {}, "tools/net6.0/any/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}, "tools/net6.0/any/Microsoft.Extensions.Options.dll": {}, "tools/net6.0/any/Microsoft.Extensions.Primitives.dll": {}, "tools/net6.0/any/Microsoft.Identity.Client.Extensions.Msal.dll": {}, "tools/net6.0/any/Microsoft.Identity.Client.dll": {}, "tools/net6.0/any/Microsoft.IdentityModel.Abstractions.dll": {}, "tools/net6.0/any/RSAKeyVaultProvider.dll": {}, "tools/net6.0/any/System.Memory.Data.dll": {}, "tools/net6.0/any/System.Security.Cryptography.ProtectedData.dll": {}, "tools/net6.0/any/runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {}}}}}, "libraries": {"AzureSignTool/4.0.1": {"sha512": "TRB6Prhz2wRjKhOUF7CvzvhjK5YH3Xy/ERg5AsRJryiuyl/O/D+p1nnHUCY0uJmGdWGC9eoR+fzfBh34MGa3CQ==", "type": "package", "path": "azuresigntool/4.0.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "azuresigntool.4.0.1.nupkg.sha512", "azuresigntool.nuspec", "tools/net6.0/any/Azure.Core.dll", "tools/net6.0/any/Azure.Identity.dll", "tools/net6.0/any/Azure.Security.KeyVault.Certificates.dll", "tools/net6.0/any/Azure.Security.KeyVault.Keys.dll", "tools/net6.0/any/AzureSign.Core.dll", "tools/net6.0/any/AzureSign.Core.pdb", "tools/net6.0/any/AzureSign.Core.xml", "tools/net6.0/any/AzureSignTool.deps.json", "tools/net6.0/any/AzureSignTool.dll", "tools/net6.0/any/AzureSignTool.pdb", "tools/net6.0/any/AzureSignTool.runtimeconfig.json", "tools/net6.0/any/DotnetToolSettings.xml", "tools/net6.0/any/McMaster.Extensions.CommandLineUtils.dll", "tools/net6.0/any/Microsoft.Bcl.AsyncInterfaces.dll", "tools/net6.0/any/Microsoft.Extensions.Configuration.Abstractions.dll", "tools/net6.0/any/Microsoft.Extensions.Configuration.Binder.dll", "tools/net6.0/any/Microsoft.Extensions.Configuration.dll", "tools/net6.0/any/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/net6.0/any/Microsoft.Extensions.DependencyInjection.dll", "tools/net6.0/any/Microsoft.Extensions.Logging.Abstractions.dll", "tools/net6.0/any/Microsoft.Extensions.Logging.Configuration.dll", "tools/net6.0/any/Microsoft.Extensions.Logging.Console.dll", "tools/net6.0/any/Microsoft.Extensions.Logging.dll", "tools/net6.0/any/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "tools/net6.0/any/Microsoft.Extensions.Options.dll", "tools/net6.0/any/Microsoft.Extensions.Primitives.dll", "tools/net6.0/any/Microsoft.Identity.Client.Extensions.Msal.dll", "tools/net6.0/any/Microsoft.Identity.Client.dll", "tools/net6.0/any/Microsoft.IdentityModel.Abstractions.dll", "tools/net6.0/any/RSAKeyVaultProvider.dll", "tools/net6.0/any/System.Memory.Data.dll", "tools/net6.0/any/System.Security.Cryptography.ProtectedData.dll", "tools/net6.0/any/runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll"]}}, "projectFileDependencyGroups": {"net6.0": ["azuresigntool >= 4.0.1"]}, "packageFolders": {"C:\\Maspects\\AzureSignTool\\.store\\.stage\\xwrl3ghl.bgf": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\qjqo1ydl.wlt\\restore.csproj", "projectName": "restore", "projectPath": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\qjqo1ydl.wlt\\restore.csproj", "packagesPath": "C:\\Maspects\\AzureSignTool\\.store\\.stage\\xwrl3ghl.bgf", "outputPath": "C:\\Maspects\\AzureSignTool\\.store\\.stage\\xwrl3ghl.bgf\\", "projectStyle": "DotnetToolReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"azuresigntool": {"target": "Package", "version": "[4.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.202\\RuntimeIdentifierGraph.json"}}, "runtimes": {"any": {"#import": []}}}}