﻿using System.Data;
using Dapper;
using iRISMobileApi.Dtos.Products;
using iRISMobileApi.Infrastructure.Data.Context;
using Microsoft.Extensions.Logging;

namespace iRISMobileApi.Infrastructure.Data.Repositories.Products
{
    public class ProductsRepository : IProductsRepository
    {
        private readonly ILogger<ProductsRepository> _logger;
        private readonly IrisMobileDBContext _dBContext;
        public ProductsRepository(ILogger<ProductsRepository> logger, IrisMobileDBContext dBContext)
        {
            _logger = logger;
            _dBContext = dBContext;
        }
        public async Task<List<object>> GetProducts(string? appType, string? searchString)
        {
            List<object> productsList;
            try
            {
                _logger.LogInformation("GetProducts of ProductsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                if (string.IsNullOrEmpty(searchString))
                {
                    searchString = null;
                }
                if(!string.IsNullOrEmpty(searchString)) 
                    _logger.LogInformation("Products with searchString: {0} are being fetched from db", searchString);
                var parameters = new DynamicParameters();
                //parameters.Add("@AppType", appType);
                parameters.Add("@SearchString", searchString);

                var procName = "";
                if (!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPSearchProducts";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPSearchProducts";
                }
                if(!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<object>(procName,
                    parameters,
                    commandType: CommandType.StoredProcedure);
                    if (!dbResult.Any())
                    {
                        _logger.LogInformation("No products are found with searchString: {0}", searchString);
                        return null;
                    }
                    productsList = dbResult.ToList();
                    _logger.LogInformation("{0} products are found with searchString: {1}", productsList.Count, searchString);
                }
                else
                {
                    _logger.LogInformation("No app type associated for proc mapping in GetProducts searchString: {0}", searchString);
                    return null;
                }
                
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetProducts of ProductsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetProducts of ProductsRepository: {ex}");
                throw;
            }
            return productsList;
        }

        public async Task<List<object>> GetProductsTemplate(string? appType, Dictionary<string, string> parameters)
        {
            List<object> dataList;
            try
            {
                _logger.LogInformation("GetProductsTemplate Template of ProductsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                
                var dynamicParams = new DynamicParameters();
                if (parameters.Count == 0)
                    dynamicParams.Add("@SearchString", "");
                else
                {
                    foreach (var keyValue in parameters)
                    {
                        dynamicParams.Add("@" + keyValue.Key, keyValue.Value);
                    }
                }
                var procName = "";
                if (!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPSearchProductsTemplate";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPSearchProductsTemplate";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Specimen"))
                {
                    procName = "uspSPCSearchProductsTemplate";
                }
                if (!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<object>(procName,
                    dynamicParams,
                    commandType: CommandType.StoredProcedure);
                    if (!dbResult.Any())
                    {
                        _logger.LogInformation("No products are found with given params: {0}", parameters);
                        return null;
                    }
                    dataList = dbResult.ToList();
                    _logger.LogInformation("{0} products are found with given params: {1}", dataList.Count, parameters);
                }
                else
                {
                    _logger.LogInformation("No app type associated for proc mapping in GetProducts parameters: {0}", parameters);
                    return null;
                }

            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetProducts of ProductsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetProducts of ProductsRepository: {ex}");
                throw;
            }
            return dataList;
        }
        
        public async Task<List<object>> GetProductDetails(string? appType, int productId)
        {
            List<object> productsDetailsList;
            try
            {
                _logger.LogInformation("GetProducts of ProductsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                if (productId != null)
                    _logger.LogInformation("Products with productId: {0} are being fetched from db", productId);
                var parameters = new DynamicParameters();
                parameters.Add("@ProductID", productId);
                parameters.Add("@AppType", appType);
                var dbResult = await _dBContext.CreateConnection().QueryAsync<object>("uspFetchProductDetails",
                    parameters,
                    commandType: CommandType.StoredProcedure);
                if (dbResult.Count() == 0)
                {
                    _logger.LogInformation("No products are found with productId: {0}", productId);
                    return null;
                }
                _logger.LogInformation("Product details are found with productId: {0}", productId);
                productsDetailsList = dbResult.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetProductDetails of ProductsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetProductDetails of ProductsRepository: {ex}");
                throw;
            }
            return productsDetailsList;
        }
        
        public async Task<List<ProductLocationSummary>> GetProductLocationSummary(string? appType, int productId)
        {
            List<ProductLocationSummary> productLocationSummaryList;
            try
            {
                _logger.LogInformation("GetProducts of ProductsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                if (productId != null)
                    _logger.LogInformation("Products with productId: {0} are being fetched from db", productId);
                var parameters = new DynamicParameters();
                parameters.Add("@ProductID", productId);
                //parameters.Add("@AppType", appType);
                var procName = "";
                if(!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPProductLocationSummary";
                } 
                else if(!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPProductLocationSummary";
                }
                if(!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<ProductLocationSummary>(procName,
                    parameters,
                    commandType: CommandType.StoredProcedure);
                    if (!dbResult.Any())
                    {
                        _logger.LogInformation("No products are found with productId: {0}", productId);
                        return null;
                    }
                    _logger.LogInformation("Product location summary are found with productId: {0}", productId);
                    productLocationSummaryList = dbResult.ToList();
                }
                else
                {
                    _logger.LogInformation("No app type associated for proc mapping in GetProductLocationSummary with productId: {0}", productId);
                    return null;
                }
                
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetProductLocationSummary of ProductsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetProductLocationSummary of ProductsRepository: {ex}");
                throw;
            }
            return productLocationSummaryList;
        }

        public async Task<List<ProductLocationDetails>> GetProductLocationDetails(string? appType, int productId)
        {
            List<ProductLocationDetails> productLocationDetailsList;
            try
            {
                _logger.LogInformation("GetProducts of ProductsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                if (productId != null)
                    _logger.LogInformation("Products with productId: {0} are being fetched from db", productId);
                var parameters = new DynamicParameters();
                parameters.Add("@ProductID", productId);
                //parameters.Add("@AppType", appType);
                var procName = "";
                if (!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPProductLocationDetails";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPProductLocationDetails";
                }
                if(!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<ProductLocationDetails>(procName,
                    parameters,
                    commandType: CommandType.StoredProcedure);
                    if (!dbResult.Any())
                    {
                        _logger.LogInformation("No products are found with productId: {0}", productId);
                        return null;
                    }
                    _logger.LogInformation("Product location details are found with productId: {0}", productId);
                    productLocationDetailsList = dbResult.ToList();
                } 
                else
                {
                    _logger.LogInformation("No app type associated for proc mapping in GetProductLocationDetails with productId: {0}", productId);
                    return null;
                }                
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetProductLocationDetails of ProductsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetProductLocationDetails of ProductsRepository: {ex}");
                throw;
            }
            return productLocationDetailsList;
        }

        public async Task<List<ProductLocationSummary>> GetProductLocationSummaryTemplate(string? appType, Dictionary<string, string> detailsParameters)
        {
            List<ProductLocationSummary> productLocationSummaryList;
            try
            {
                _logger.LogInformation("GetProducts of ProductsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                var dynamicParams = new DynamicParameters();
                if (detailsParameters.Count == 0)
                    dynamicParams.Add("@SearchString", "");
                else
                {
                    foreach (var keyValue in detailsParameters)
                    {
                        dynamicParams.Add("@" + keyValue.Key, keyValue.Value);
                    }
                }
                var procName = "";
                if (!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPProductLocationSummary";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPProductLocationSummary";
                }
                if (!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<ProductLocationSummary>(procName,
                    dynamicParams,
                    commandType: CommandType.StoredProcedure);
                    if (!dbResult.Any())
                    {
                        _logger.LogInformation("No products are found for given parameters");
                        return null;
                    }
                    _logger.LogInformation("Product location summary are found for given parameters");
                    productLocationSummaryList = dbResult.ToList();
                }
                else
                {
                    _logger.LogInformation("No app type associated for proc mapping in GetProductLocationSummary");
                    return null;
                }

            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetProductLocationSummary of ProductsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetProductLocationSummary of ProductsRepository: {ex}");
                throw;
            }
            return productLocationSummaryList;
        }

        public async Task<List<ProductLocationDetails>> GetProductLocationDetailsTemplate(string? appType, Dictionary<string, string> detailsParameters)
        {
            List<ProductLocationDetails> productLocationDetailsList;
            try
            {
                _logger.LogInformation("GetProducts of ProductsRepository is invoked");
                if (string.IsNullOrEmpty(appType))
                {
                    appType = null;
                }
                var dynamicParams = new DynamicParameters();
                if (detailsParameters.Count == 0)
                    dynamicParams.Add("@SearchString", "");
                else
                {
                    foreach (var keyValue in detailsParameters)
                    {
                        dynamicParams.Add("@" + keyValue.Key, keyValue.Value);
                    }
                }
                var procName = "";
                if (!string.IsNullOrEmpty(appType) && appType.Contains("Supply"))
                {
                    procName = "uspSUPProductLocationDetails";
                }
                else if (!string.IsNullOrEmpty(appType) && appType.Contains("Scope"))
                {
                    procName = "uspSCPProductLocationDetails";
                }
                if (!string.IsNullOrEmpty(procName))
                {
                    var dbResult = await _dBContext.CreateConnection().QueryAsync<ProductLocationDetails>(procName,
                    dynamicParams,
                    commandType: CommandType.StoredProcedure);
                    if (!dbResult.Any())
                    {
                        _logger.LogInformation("No products are found for given parameters");
                        return null;
                    }
                    _logger.LogInformation("Product location details are found for given parameters");
                    productLocationDetailsList = dbResult.ToList();
                }
                else
                {
                    _logger.LogInformation("No app type associated for proc mapping in GetProductLocationDetails");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error while calling GetProductLocationDetails of ProductsRepository: {ex.Message}");
                _logger.LogDebug($"Error while calling GetProductLocationDetails of ProductsRepository: {ex}");
                throw;
            }
            return productLocationDetailsList;
        }
    }
}
