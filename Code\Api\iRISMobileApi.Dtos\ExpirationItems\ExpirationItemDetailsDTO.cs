﻿using System.Runtime.Serialization;

namespace iRISMobileApi.Dtos.ExpirationItems
{
    [Serializable]
    [DataContract]
    public class ExpirationItemDetailsDTO
    {
        [DataMember]
        public string CatalogNo { get; set; }
        [DataMember]
        public string ExpiredDate { get; set; }
        [DataMember]
        public string RFID { get; set; }
        [DataMember]
        public string ClusterName { get; set; }
        [DataMember]
        public string CabinetName { get; set; }
        [DataMember]
        public string CabinetType { get; set; }
        [DataMember]
        public string ClusterLocation { get; set; }
        [DataMember]
        public string Location { get; set; }
        [DataMember]
        public string SerialNo { get; set; }
        [DataMember]
        public string Status { get; set; }
        [DataMember]
        public string ScanDate { get; set; }
        [DataMember]
        public string ScanUser { get; set; }
        [DataMember]
        public string Label_RFID { get; set; }

        [DataMember]
        public string Label_SerialNo { get; set; }

        [DataMember]
        public string Label_Expiration { get; set; }
        [DataMember]
        public string Label_ScanDate { get; set; }
        [DataMember]
        public string Label_ScanUser { get; set; }
        [DataMember]
        public string Label_Status { get; set; }
    }
}
