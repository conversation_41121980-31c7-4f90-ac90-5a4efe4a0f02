
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.7.34009.444
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{48912EDC-F5FE-4C49-B7EC-76DC1C62823A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iRISMobileApi.UnitTest", "Tests\iRISMobileApi.UnitTest\iRISMobileApi.UnitTest.csproj", "{4AFD87F6-C406-4BF0-8C4F-F9729FF5FF92}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Library", "Library", "{214EB457-BF0D-4274-8402-3DFC1FBA824C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Api", "Api", "{622D11DA-132C-4E29-92E1-7CCA95B77B8D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iRISMobileApi", "Api\iRISMobileApi\iRISMobileApi.csproj", "{A0EAA907-F35F-4C00-9321-5D5A7D6BB0E2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iRISMobileApi.Core", "Library\iRISMobileApi.Core\iRISMobileApi.Core.csproj", "{8F525161-9728-41E9-8602-C371E5F0B931}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iRISMobileApi.Infrastructure", "Library\iRISMobileApi.Infrastructure\iRISMobileApi.Infrastructure.csproj", "{6DB4C3AF-019C-4A30-A536-88BF0C820636}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "iRISMobileApi.Dtos", "Api\iRISMobileApi.Dtos\iRISMobileApi.Dtos.csproj", "{A670E746-B856-4B3E-9836-A1AC7AECD7BE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4AFD87F6-C406-4BF0-8C4F-F9729FF5FF92}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4AFD87F6-C406-4BF0-8C4F-F9729FF5FF92}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4AFD87F6-C406-4BF0-8C4F-F9729FF5FF92}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4AFD87F6-C406-4BF0-8C4F-F9729FF5FF92}.Release|Any CPU.Build.0 = Release|Any CPU
		{A0EAA907-F35F-4C00-9321-5D5A7D6BB0E2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A0EAA907-F35F-4C00-9321-5D5A7D6BB0E2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A0EAA907-F35F-4C00-9321-5D5A7D6BB0E2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A0EAA907-F35F-4C00-9321-5D5A7D6BB0E2}.Release|Any CPU.Build.0 = Release|Any CPU
		{8F525161-9728-41E9-8602-C371E5F0B931}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F525161-9728-41E9-8602-C371E5F0B931}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F525161-9728-41E9-8602-C371E5F0B931}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F525161-9728-41E9-8602-C371E5F0B931}.Release|Any CPU.Build.0 = Release|Any CPU
		{6DB4C3AF-019C-4A30-A536-88BF0C820636}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6DB4C3AF-019C-4A30-A536-88BF0C820636}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6DB4C3AF-019C-4A30-A536-88BF0C820636}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6DB4C3AF-019C-4A30-A536-88BF0C820636}.Release|Any CPU.Build.0 = Release|Any CPU
		{A670E746-B856-4B3E-9836-A1AC7AECD7BE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A670E746-B856-4B3E-9836-A1AC7AECD7BE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A670E746-B856-4B3E-9836-A1AC7AECD7BE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A670E746-B856-4B3E-9836-A1AC7AECD7BE}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{4AFD87F6-C406-4BF0-8C4F-F9729FF5FF92} = {48912EDC-F5FE-4C49-B7EC-76DC1C62823A}
		{A0EAA907-F35F-4C00-9321-5D5A7D6BB0E2} = {622D11DA-132C-4E29-92E1-7CCA95B77B8D}
		{8F525161-9728-41E9-8602-C371E5F0B931} = {214EB457-BF0D-4274-8402-3DFC1FBA824C}
		{6DB4C3AF-019C-4A30-A536-88BF0C820636} = {214EB457-BF0D-4274-8402-3DFC1FBA824C}
		{A670E746-B856-4B3E-9836-A1AC7AECD7BE} = {622D11DA-132C-4E29-92E1-7CCA95B77B8D}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4F3BDE75-AE93-4497-B4B0-E083CED5DF4D}
	EndGlobalSection
EndGlobal
