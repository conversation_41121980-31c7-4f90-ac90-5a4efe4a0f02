﻿namespace iRISMobileApi.Dtos.ExpirationItems
{
    public class ExpirationItemDetailsResponse
    {
        public string GroupTitle { get; set; }
        public List<ExpirationItemDetailsModel> AllItems { get; set; }
        public int Qty { get; set; }

        public ExpirationItemDetailsResponse(string groupTitle, List<ExpirationItemDetailsModel> allItems, int qty)
        {
            GroupTitle = groupTitle;
            AllItems = allItems;
            Qty = qty;
        }
    }

    public class ExpirationItemDetailsModel
    {
        public int Index { get; set; }
        public string CatalogNo { get; set; }
        public DateTime? ExpiredDate { get; set; }
        public string RFID { get; set; }
        public string ClusterName { get; set; }
        public string CabinetName { get; set; }
        public string CabinetType { get; set; }
        public string ClusterLocation { get; set; }
        public string Location { get; set; }
        public string SerialNo { get; set; }
        public string ItemStatus { get; set; }
        public DateTime? ScanDate { get; set; }
        public string ScanUser { get; set; }
        public string Label_Expiration { get; set; }
        public string Label_RFID { get; set; }
        public string Label_SerialNo { get; set; }
        public string Label_ScanDate { get; set; }
        public string Label_ScanUser { get; set; }
        public string Label_Status { get; set; }
    }
}
