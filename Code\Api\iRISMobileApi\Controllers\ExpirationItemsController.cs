﻿using System.Net;
using iRISMobileApi.Core.ExpiredItem;
using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.Common;
using iRISMobileApi.Dtos.Constants;
using iRISMobileApi.Dtos.ExpirationItems;
using Microsoft.AspNetCore.Mvc;

namespace iRISMobileApi.Controllers
{
    [ApiController]
    [Produces("application/json")]
    public class ExpirationItemsController : ControllerBase
    {
        private readonly ILogger<ExpirationItemsController> _logger;
        private readonly IExpirationItemsService _expirationItemsService;

        public ExpirationItemsController(ILogger<ExpirationItemsController> logger, IExpirationItemsService expirationItemsService)
        {
            _logger = logger;
            _expirationItemsService = expirationItemsService;
        }

        [Route("expiration-items"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<ExpirationItemsResponse>>> FetchExpirationItems([FromBody] ExpirationItemsRequest expirationItemsRequest)
        {
            ApiResponse<ExpirationItemsResponse> result;
            try
            {
                _logger.LogInformation("expiration-items route is invoked");
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<ExpirationItemsResponse>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for fetchExpirationItems",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("FetchExpirationItems route generated failure response {0}", result.ToJson());
                    return result;
                }
                ExpirationItemsResponse response = await _expirationItemsService.GetExpirationItems(expirationItemsRequest.requestingApp.appType, expirationItemsRequest.expirationItemsInfo.searchString);
                if (response == null || response.expirationItemsList?.Count < 1)
                {
                    result = new ApiResponse<ExpirationItemsResponse>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.NoContent,
                        error = new Error
                        {
                            message = "no expiration items found for given search characters"
                        }
                    };
                    _logger.LogInformation("expiration-items route generated failure response {0}", result.ToJson());
                    return result;
                }
                result = new ApiResponse<ExpirationItemsResponse>
                {
                    status = ApiResponseStatus.SUCCESS,
                    code = HttpStatusCode.OK,
                    data = response
                };
                _logger.LogInformation("expiration-items route generated success response {0}", result.ToJson());
            }
            catch (Exception ex)
            {
                result = new ApiResponse<ExpirationItemsResponse>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to fetch expiration items list due to internal server error."
                    }
                };
                _logger.LogError("expiration-items route generated error response {0}", result.ToJson());
            }
            return result;
        }

        [Route("expiration-item-details"), HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<ApiResponse<List<ExpirationItemDetailsResponse>>>> FetchExpirationItemDetails([FromBody] ExpirationItemDetailsRequest expirationItemsDetailsRequest)
        {
            ApiResponse<List<ExpirationItemDetailsResponse>> result;
            try
            {
                _logger.LogInformation("expiration-item-details route is invoked");
                if (!ModelState.IsValid)
                {
                    result = new ApiResponse<List<ExpirationItemDetailsResponse>>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.BadRequest,
                        error = new Error
                        {
                            message = "Invalid api request for fetchExpirationItemDetails",
                            details = "mandatory fields are missing from the request."
                        }
                    };
                    _logger.LogInformation("FetchExpirationItemDetails route generated failure response {0}", result.ToJson());
                    return result;
                }
                var response = await _expirationItemsService.GetExpirationItemDetails(expirationItemsDetailsRequest.requestingApp.appType, expirationItemsDetailsRequest.expirationItemInfo.catalogNo);
                if (response == null)
                {
                    result = new ApiResponse<List<ExpirationItemDetailsResponse>>
                    {
                        status = ApiResponseStatus.FAIL,
                        code = HttpStatusCode.NoContent,
                        error = new Error
                        {
                            message = "no expiration items found for given search characters"
                        }
                    };
                    _logger.LogInformation("expiration-item-details route generated failure response {0}", result.ToJson());
                    return result;
                }
                result = new ApiResponse<List<ExpirationItemDetailsResponse>> {
                    status = ApiResponseStatus.SUCCESS,
                    code = HttpStatusCode.OK,
                    data = response
                };
                _logger.LogInformation("expiration-item-details route generated success response {0}", result.ToJson());
            }
            catch (Exception ex)
            {
                result = new ApiResponse<List<ExpirationItemDetailsResponse>>
                {
                    status = ApiResponseStatus.ERROR,
                    code = HttpStatusCode.InternalServerError,
                    error = new Error
                    {
                        message = "Internal server error occured",
                        details = "Unable to fetch expiration item details due to internal server error."
                    }
                };
                _logger.LogError("expiration-item-details route generated error response {0}", result.ToJson());
            }
            return result;
        }

    }
}
