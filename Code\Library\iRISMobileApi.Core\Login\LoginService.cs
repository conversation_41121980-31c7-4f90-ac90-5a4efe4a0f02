﻿using iRISMobileApi.Core.Utils;
using iRISMobileApi.Dtos.Login;
using iRISMobileApi.Dtos.Model.Configs;
using iRISMobileApi.Infrastructure.Data.Repositories.Login;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace iRISMobileApi.Core.Login
{
    public class LoginService : ILoginService
    {
        private readonly ILogger<LoginService> _logger;
        private readonly IOptions<ActiveDirectoryOptions> _activeDirectoryOptions; 
        private readonly IActiveDirectoryService _activeDirectoryService;
        private readonly ILoginRepository _loginRepository;

        public LoginService(ILogger<LoginService> logger, IOptions<ActiveDirectoryOptions> activeDirectoryOptions, IActiveDirectoryService activeDirectoryService, ILoginRepository loginRepository)
        {
            _logger = logger;
            _activeDirectoryOptions = activeDirectoryOptions;
            _activeDirectoryService = activeDirectoryService;
            _loginRepository = loginRepository;
        }

        public async Task<UserDetails?> AuthenticateUser(LoginRequest loginRequest)
        {
            try
            {
                _logger.LogInformation("AuthenticateUser of LoginService is invoked");
                _logger.LogInformation("Username: {0} is trying to login", loginRequest.userInfo.username);
                User userData;
                if (_activeDirectoryOptions.Value.IsEnabled)
                {
                    _logger.LogInformation("Username: {0} is getting authenticated with ActiveDirectory", loginRequest.userInfo.username);
                    userData = await _activeDirectoryService.AuthenticateUser(loginRequest.userInfo.username, loginRequest.userInfo.password);
                }
                else
                {
                    _logger.LogInformation("Username: {0} is getting authenticated from DB", loginRequest.userInfo.username);
                    //var appType = "iRISupply";
                    var appType = await _loginRepository.FetchDeployedAppType();
                    if(!string.IsNullOrEmpty(appType))
                    {
                        userData = await _loginRepository.AuthenticateUserFromDB(loginRequest, appType);
                    }
                    else
                    {
                        userData = null;
                    }                    
                }
                if (userData != null)
                {
                    _logger.LogDebug("UserInfo received for Username: {0} is {1}", loginRequest.userInfo.username, userData.ToJson());
                    UserDetails userDetails = new()
                    {
                        id = userData.UserID,
                        sessionId = userData.UserID.ToString() + "_"  +DateTime.Now.ToLongTimeString(),
                        displayName = userData.FirstName + " " + userData.LastName,
                        email = userData.Email,
                        role = userData.UserGroup
                    };
                    return userDetails;
                }
                else
                {
                    _logger.LogInformation("Service response for AuthenticateUser does not generate any result");
                    return null;
                }
            }
            catch(Exception ex)
            {
                _logger.LogError($"Error occured while calling AuthenticateUser in LoginService: {ex.Message}");
                _logger.LogDebug($"Error occured while calling AuthenticateUser in LoginService: {ex.ToJson()}");
                return new UserDetails { id = -1 };
            }
        }

        public async Task<List<WorkFlowDetails>> FetchAppScreensByUser(string username)
        {
            try
            {
                List<WorkFlowDetails> workFlowList = new List<WorkFlowDetails>();
                _logger.LogInformation("Username: {0} is fetching AppScreens in LoginService", username);
                var appScreenList = await _loginRepository.FetchAppScreensByUser(username);                
                if (appScreenList != null && appScreenList.Count > 0)
                {
                    _logger.LogInformation("AppScreens for Username: {0} are fetch successfully in LoginService", username);
                    foreach (var appScreenDetails in appScreenList)
                    {
                        WorkFlowDetails wfd = new()
                        {
                            workFlowName = appScreenDetails.AppScreenName,
                            displayName = appScreenDetails.AppScreenName,
                            appType = appScreenDetails.AppType,
                            iconName = appScreenDetails.IconName,
                            iconURL = appScreenDetails.IconURL
                        };

                        if (appScreenDetails.AppScreenName != null && (appScreenDetails.AppScreenName.Contains("Product") || appScreenDetails.AppScreenName.Contains("Search")))
                            wfd.action = "products";
                        else if (appScreenDetails.AppScreenName != null && (appScreenDetails.AppScreenName.Contains("Expire")))
                            wfd.action = "expiration-items";
                        else if(appScreenDetails.AppScreenName != null && (appScreenDetails.AppScreenName.Contains("Alerts")))
                            wfd.action = "alerts";

                        workFlowList.Add(wfd);
                    }                    
                }
                return workFlowList;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error occured while fetching AppScreens in LoginService: {ex.Message}");
                _logger.LogDebug($"Error occured while fetching AppScreens in LoginService: {ex.ToJson()}");
                return null;
            }
        }
    }
}
